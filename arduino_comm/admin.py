from django.contrib import admin
from .models import ComandoArduino, EstadoArduino, LogComunicacion, ConfiguracionArduino


@admin.register(ComandoArduino)
class ComandoArduinoAdmin(admin.ModelAdmin):
    list_display = ['tipo_comando', 'estado', 'prioridad', 'intentos', 'created_at']
    list_filter = ['tipo_comando', 'estado', 'prioridad']
    search_fields = ['comando', 'respuesta']
    ordering = ['prioridad', '-created_at']
    readonly_fields = ['created_at', 'tiempo_envio', 'tiempo_respuesta']


@admin.register(EstadoArduino)
class EstadoArduinoAdmin(admin.ModelAdmin):
    list_display = ['conectado', 'puerto_serie', 'version_firmware', 'ultimo_ping', 'errores_comunicacion']
    readonly_fields = ['updated_at', 'ultimo_ping']


@admin.register(LogComunicacion)
class LogComunicacionAdmin(admin.ModelAdmin):
    list_display = ['tipo', 'mensaje', 'timestamp', 'puerto_serie']
    list_filter = ['tipo', 'timestamp']
    search_fields = ['mensaje', 'datos_raw']
    ordering = ['-timestamp']
    readonly_fields = ['timestamp']


@admin.register(ConfiguracionArduino)
class ConfiguracionArduinoAdmin(admin.ModelAdmin):
    list_display = ['puerto_serie', 'baudrate', 'activa', 'auto_reconectar', 'debug_mode']
    list_filter = ['activa', 'auto_reconectar', 'debug_mode']
