from django.core.management.base import BaseCommand
from django.utils import timezone
from arduino_comm.models import ComandoArduino, EstadoArduino, LogComunicacion


class Command(BaseCommand):
    help = 'Gestiona la cola de comandos Arduino'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['list', 'clear', 'retry', 'stats', 'add'],
            help='Acción a realizar'
        )
        parser.add_argument(
            '--estado',
            choices=['pendiente', 'enviado', 'ejecutado', 'error', 'timeout'],
            help='Filtrar por estado (para list)'
        )
        parser.add_argument(
            '--comando-id',
            type=int,
            help='ID del comando específico (para retry)'
        )
        parser.add_argument(
            '--tipo',
            choices=['read_sensor', 'activate_actuator', 'deactivate_actuator', 
                    'get_status', 'set_config', 'ping', 'reset'],
            help='Tipo de comando (para add)'
        )
        parser.add_argument(
            '--comando-texto',
            type=str,
            help='Texto del comando (para add)'
        )
        parser.add_argument(
            '--prioridad',
            type=int,
            default=5,
            help='Prioridad del comando 1-10 (para add, default: 5)'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'list':
            self._list_commands(options.get('estado'))
        elif action == 'clear':
            self._clear_commands()
        elif action == 'retry':
            self._retry_command(options.get('comando_id'))
        elif action == 'stats':
            self._show_stats()
        elif action == 'add':
            self._add_command(options)

    def _list_commands(self, estado_filter=None):
        """Lista comandos en la cola"""
        self.stdout.write('=== COLA DE COMANDOS ARDUINO ===')
        
        queryset = ComandoArduino.objects.all()
        if estado_filter:
            queryset = queryset.filter(estado=estado_filter)
        
        comandos = queryset.order_by('-created_at')[:20]
        
        if not comandos:
            self.stdout.write('No hay comandos en la cola')
            return
        
        self.stdout.write(f'Mostrando últimos {comandos.count()} comandos:')
        self.stdout.write('-' * 80)
        
        for comando in comandos:
            estado_color = self._get_estado_color(comando.estado)
            tiempo_respuesta = ''
            
            if comando.tiempo_respuesta_ms():
                tiempo_respuesta = f' ({comando.tiempo_respuesta_ms():.1f}ms)'
            
            estado_str = f'{comando.estado.upper():10s}'
            tipo_str = f'{comando.get_tipo_comando_display():15s}'

            self.stdout.write(
                f'ID: {comando.id:3d} | '
                f'{estado_color(estado_str)} | '
                f'{tipo_str} | '
                f'P:{comando.prioridad} | '
                f'{comando.created_at.strftime("%H:%M:%S")}{tiempo_respuesta}'
            )
            
            if comando.error_mensaje:
                self.stdout.write(f'     Error: {comando.error_mensaje}')
            
            if len(comando.comando) > 50:
                self.stdout.write(f'     Comando: {comando.comando[:50]}...')
            else:
                self.stdout.write(f'     Comando: {comando.comando}')

    def _clear_commands(self):
        """Limpia comandos completados o con error"""
        estados_a_limpiar = ['ejecutado', 'error', 'timeout']
        
        count = ComandoArduino.objects.filter(
            estado__in=estados_a_limpiar
        ).count()
        
        if count == 0:
            self.stdout.write('No hay comandos para limpiar')
            return
        
        ComandoArduino.objects.filter(
            estado__in=estados_a_limpiar
        ).delete()
        
        self.stdout.write(
            self.style.SUCCESS(f'✓ Eliminados {count} comandos completados/con error')
        )

    def _retry_command(self, comando_id):
        """Reintenta un comando específico"""
        if not comando_id:
            self.stdout.write(
                self.style.ERROR('Debe especificar --comando-id')
            )
            return
        
        try:
            comando = ComandoArduino.objects.get(id=comando_id)
            
            if comando.estado == 'pendiente':
                self.stdout.write(
                    self.style.WARNING('El comando ya está pendiente')
                )
                return
            
            # Resetear estado para reintento
            comando.estado = 'pendiente'
            comando.error_mensaje = ''
            comando.respuesta = ''
            comando.tiempo_envio = None
            comando.tiempo_respuesta = None
            comando.save()
            
            self.stdout.write(
                self.style.SUCCESS(f'✓ Comando {comando_id} marcado para reintento')
            )
            
        except ComandoArduino.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Comando {comando_id} no encontrado')
            )

    def _show_stats(self):
        """Muestra estadísticas de la cola"""
        self.stdout.write('=== ESTADÍSTICAS ARDUINO ===')
        
        # Estadísticas de comandos
        total_comandos = ComandoArduino.objects.count()
        pendientes = ComandoArduino.objects.filter(estado='pendiente').count()
        ejecutados = ComandoArduino.objects.filter(estado='ejecutado').count()
        errores = ComandoArduino.objects.filter(estado='error').count()
        
        self.stdout.write(f'Total comandos: {total_comandos}')
        self.stdout.write(f'Pendientes: {pendientes}')
        self.stdout.write(f'Ejecutados: {ejecutados}')
        self.stdout.write(f'Con error: {errores}')
        
        if total_comandos > 0:
            tasa_exito = (ejecutados / total_comandos) * 100
            self.stdout.write(f'Tasa de éxito: {tasa_exito:.1f}%')
        
        # Estado del Arduino
        try:
            estado = EstadoArduino.objects.first()
            if estado:
                self.stdout.write('\n--- Estado del Arduino ---')
                self.stdout.write(f'Conectado: {"Sí" if estado.conectado else "No"}')
                self.stdout.write(f'Puerto: {estado.puerto_serie}')
                self.stdout.write(f'Baudrate: {estado.baudrate}')
                self.stdout.write(f'Comandos ejecutados: {estado.comandos_ejecutados}')
                self.stdout.write(f'Errores comunicación: {estado.errores_comunicacion}')
                
                if estado.ultimo_ping:
                    tiempo_desde_ping = timezone.now() - estado.ultimo_ping
                    self.stdout.write(f'Último ping: hace {tiempo_desde_ping.seconds}s')
                
                if estado.uptime_segundos:
                    uptime_horas = estado.uptime_segundos / 3600
                    self.stdout.write(f'Uptime Arduino: {uptime_horas:.1f}h')
        except Exception as e:
            self.stdout.write(f'Error obteniendo estado: {str(e)}')
        
        # Logs recientes
        logs_recientes = LogComunicacion.objects.order_by('-timestamp')[:5]
        if logs_recientes:
            self.stdout.write('\n--- Últimos logs ---')
            for log in logs_recientes:
                self.stdout.write(
                    f'{log.timestamp.strftime("%H:%M:%S")} - '
                    f'{log.get_tipo_display()}: {log.mensaje[:50]}'
                )

    def _add_command(self, options):
        """Añade un comando a la cola"""
        tipo = options.get('tipo')
        comando_texto = options.get('comando_texto')
        prioridad = options.get('prioridad', 5)
        
        if not tipo or not comando_texto:
            self.stdout.write(
                self.style.ERROR('Debe especificar --tipo y --comando-texto')
            )
            return
        
        comando = ComandoArduino.objects.create(
            tipo_comando=tipo,
            comando=comando_texto,
            prioridad=prioridad
        )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'✓ Comando añadido con ID {comando.id} (prioridad {prioridad})'
            )
        )

    def _get_estado_color(self, estado):
        """Devuelve el estilo de color según el estado"""
        if estado == 'ejecutado':
            return self.style.SUCCESS
        elif estado == 'error' or estado == 'timeout':
            return self.style.ERROR
        elif estado == 'pendiente':
            return self.style.WARNING
        else:
            return lambda x: x  # Sin color
