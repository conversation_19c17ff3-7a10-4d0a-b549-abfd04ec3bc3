import time
import signal
import sys
from django.core.management.base import BaseCommand
from django.utils import timezone
from arduino_comm.services import ArduinoService
from arduino_comm.models import ComandoArduino, EstadoArduino
from core.models import Sensor, LecturaSensor


class Command(BaseCommand):
    help = 'Monitorea continuamente la comunicación con Arduino'

    def __init__(self):
        super().__init__()
        self.running = True
        self.arduino_service = None

    def add_arguments(self, parser):
        parser.add_argument(
            '--intervalo',
            type=int,
            default=30,
            help='Intervalo entre lecturas en segundos (default: 30)'
        )
        parser.add_argument(
            '--mock',
            action='store_true',
            help='Usar datos simulados (para pruebas sin Arduino físico)'
        )
        parser.add_argument(
            '--sensores-solo',
            action='store_true',
            help='Solo leer sensores, no procesar comandos pendientes'
        )

    def handle(self, *args, **options):
        self.intervalo = options['intervalo']
        self.mock_mode = options['mock']
        self.sensores_solo = options['sensores_solo']
        
        # Configurar manejo de señales para cierre limpio
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.stdout.write(
            self.style.SUCCESS('=== MONITOR ARDUINO INICIADO ===')
        )
        self.stdout.write(f'Intervalo: {self.intervalo} segundos')
        self.stdout.write(f'Modo simulado: {"Sí" if self.mock_mode else "No"}')
        self.stdout.write(f'Solo sensores: {"Sí" if self.sensores_solo else "No"}')
        self.stdout.write('Presiona Ctrl+C para detener')
        self.stdout.write('-' * 50)
        
        try:
            # Inicializar servicio Arduino
            self.arduino_service = ArduinoService(mock_mode=self.mock_mode)
            
            # Bucle principal de monitoreo
            ciclo = 0
            while self.running:
                ciclo += 1
                self.stdout.write(f'\n--- Ciclo {ciclo} ({timezone.now()}) ---')
                
                try:
                    # 1. Verificar conexión con ping
                    if self._check_connection():
                        self.stdout.write(self.style.SUCCESS('✓ Conexión OK'))
                        
                        # 2. Leer sensores
                        self._read_sensors()
                        
                        # 3. Procesar comandos pendientes (si no es solo sensores)
                        if not self.sensores_solo:
                            self._process_pending_commands()
                        
                        # 4. Actualizar estado del sistema
                        self._update_system_status()
                        
                    else:
                        self.stdout.write(self.style.ERROR('✗ Sin conexión'))
                        
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error en ciclo {ciclo}: {str(e)}')
                    )
                
                # Esperar antes del siguiente ciclo
                if self.running:
                    time.sleep(self.intervalo)
                    
        except KeyboardInterrupt:
            pass
        finally:
            self._cleanup()

    def _signal_handler(self, signum, frame):
        """Maneja señales de cierre"""
        self.stdout.write('\nRecibida señal de cierre...')
        self.running = False

    def _check_connection(self):
        """Verifica la conexión con Arduino"""
        try:
            return self.arduino_service.ping()
        except Exception as e:
            self.stdout.write(f'Error en ping: {str(e)}')
            return False

    def _read_sensors(self):
        """Lee todos los sensores activos"""
        sensores = Sensor.objects.filter(activo=True)
        lecturas_exitosas = 0
        
        self.stdout.write(f'Leyendo {sensores.count()} sensores...')
        
        for sensor in sensores:
            try:
                valor = self.arduino_service.read_sensor(
                    f'A{sensor.pin_arduino}', 
                    sensor.tipo
                )
                
                if valor is not None:
                    # Crear lectura en la base de datos
                    LecturaSensor.objects.create(
                        sensor=sensor,
                        valor=valor
                    )
                    lecturas_exitosas += 1
                    
                    self.stdout.write(
                        f'  {sensor.nombre}: {valor} {sensor.unidad_medida}'
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'  {sensor.nombre}: Sin datos')
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  {sensor.nombre}: Error - {str(e)}')
                )
        
        self.stdout.write(f'Lecturas exitosas: {lecturas_exitosas}/{sensores.count()}')

    def _process_pending_commands(self):
        """Procesa comandos pendientes en la cola"""
        comandos_pendientes = ComandoArduino.objects.filter(
            estado='pendiente'
        ).order_by('prioridad', 'created_at')[:10]  # Procesar máximo 10 por ciclo
        
        if comandos_pendientes:
            self.stdout.write(f'Procesando {comandos_pendientes.count()} comandos pendientes...')
            
            for comando in comandos_pendientes:
                try:
                    success = self.arduino_service.execute_command(comando)
                    if success:
                        self.stdout.write(
                            f'  ✓ Comando {comando.id}: {comando.get_tipo_comando_display()}'
                        )
                    else:
                        self.stdout.write(
                            self.style.WARNING(
                                f'  ⚠ Comando {comando.id}: Falló'
                            )
                        )
                        
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f'  ✗ Comando {comando.id}: Error - {str(e)}'
                        )
                    )
        else:
            self.stdout.write('No hay comandos pendientes')

    def _update_system_status(self):
        """Actualiza el estado del sistema"""
        try:
            status = self.arduino_service.get_system_status()
            if status:
                self.stdout.write('Estado del sistema actualizado:')
                self.stdout.write(f'  Uptime: {status.get("uptime", 0)} seg')
                self.stdout.write(f'  Memoria libre: {status.get("memory", 0)} bytes')
                if status.get('temperature'):
                    self.stdout.write(f'  Temperatura CPU: {status["temperature"]}°C')
                if status.get('voltage'):
                    self.stdout.write(f'  Voltaje: {status["voltage"]}V')
            else:
                self.stdout.write(self.style.WARNING('No se pudo obtener estado del sistema'))
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error actualizando estado: {str(e)}')
            )

    def _cleanup(self):
        """Limpieza al cerrar"""
        self.stdout.write('\nCerrando monitor...')
        
        if self.arduino_service:
            try:
                self.arduino_service.close()
                self.stdout.write('Conexión Arduino cerrada')
            except Exception as e:
                self.stdout.write(f'Error cerrando Arduino: {str(e)}')
        
        # Mostrar estadísticas finales
        try:
            estado = EstadoArduino.objects.first()
            if estado:
                self.stdout.write('\n=== ESTADÍSTICAS FINALES ===')
                self.stdout.write(f'Comandos ejecutados: {estado.comandos_ejecutados}')
                self.stdout.write(f'Errores de comunicación: {estado.errores_comunicacion}')
                self.stdout.write(f'Último ping: {estado.ultimo_ping}')
        except Exception:
            pass
        
        self.stdout.write(
            self.style.SUCCESS('Monitor Arduino detenido correctamente')
        )
