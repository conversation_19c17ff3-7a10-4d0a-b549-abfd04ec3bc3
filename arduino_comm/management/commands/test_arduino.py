import time
import json
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from arduino_comm.models import ComandoArduino, EstadoArduino, LogComunicacion, ConfiguracionArduino
from arduino_comm.services import ArduinoService


class Command(BaseCommand):
    help = 'Prueba la comunicación con Arduino Mega'

    def add_arguments(self, parser):
        parser.add_argument(
            '--puerto',
            type=str,
            default=getattr(settings, 'ARDUINO_PORT', '/dev/ttyUSB0'),
            help='Puerto serie del Arduino (default: /dev/ttyUSB0)'
        )
        parser.add_argument(
            '--baudrate',
            type=int,
            default=getattr(settings, 'ARDUINO_BAUDRATE', 9600),
            help='Velocidad de comunicación (default: 9600)'
        )
        parser.add_argument(
            '--timeout',
            type=int,
            default=5,
            help='Timeout en segundos (default: 5)'
        )
        parser.add_argument(
            '--comando',
            type=str,
            choices=['ping', 'status', 'sensors', 'actuators', 'test_all'],
            default='ping',
            help='Comando a ejecutar (default: ping)'
        )
        parser.add_argument(
            '--mock',
            action='store_true',
            help='Usar datos simulados (para pruebas sin Arduino físico)'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('=== PRUEBA DE COMUNICACIÓN ARDUINO ===')
        )
        
        puerto = options['puerto']
        baudrate = options['baudrate']
        timeout = options['timeout']
        comando = options['comando']
        mock_mode = options['mock']
        
        self.stdout.write(f'Puerto: {puerto}')
        self.stdout.write(f'Baudrate: {baudrate}')
        self.stdout.write(f'Timeout: {timeout}s')
        self.stdout.write(f'Comando: {comando}')
        self.stdout.write(f'Modo simulado: {"Sí" if mock_mode else "No"}')
        self.stdout.write('-' * 50)
        
        try:
            # Crear o actualizar configuración
            config, created = ConfiguracionArduino.objects.get_or_create(
                activa=True,
                defaults={
                    'puerto_serie': puerto,
                    'baudrate': baudrate,
                    'timeout_segundos': timeout,
                    'debug_mode': True
                }
            )
            
            if not created:
                config.puerto_serie = puerto
                config.baudrate = baudrate
                config.timeout_segundos = timeout
                config.debug_mode = True
                config.save()
            
            # Inicializar servicio Arduino
            arduino_service = ArduinoService(mock_mode=mock_mode)
            
            # Ejecutar comando específico
            if comando == 'ping':
                self._test_ping(arduino_service)
            elif comando == 'status':
                self._test_status(arduino_service)
            elif comando == 'sensors':
                self._test_sensors(arduino_service)
            elif comando == 'actuators':
                self._test_actuators(arduino_service)
            elif comando == 'test_all':
                self._test_all(arduino_service)
            
            self.stdout.write(
                self.style.SUCCESS('\n=== PRUEBA COMPLETADA ===')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error durante la prueba: {str(e)}')
            )
            raise CommandError(f'Falló la comunicación con Arduino: {str(e)}')

    def _test_ping(self, arduino_service):
        """Prueba básica de ping"""
        self.stdout.write('\n1. PRUEBA DE PING')
        self.stdout.write('-' * 20)
        
        try:
            response = arduino_service.ping()
            if response:
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Ping exitoso: {response}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('✗ Ping falló')
                )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Error en ping: {str(e)}')
            )

    def _test_status(self, arduino_service):
        """Prueba de estado del sistema"""
        self.stdout.write('\n2. PRUEBA DE ESTADO')
        self.stdout.write('-' * 20)
        
        try:
            status = arduino_service.get_system_status()
            if status:
                self.stdout.write(
                    self.style.SUCCESS('✓ Estado obtenido:')
                )
                for key, value in status.items():
                    self.stdout.write(f'  {key}: {value}')
            else:
                self.stdout.write(
                    self.style.ERROR('✗ No se pudo obtener el estado')
                )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Error obteniendo estado: {str(e)}')
            )

    def _test_sensors(self, arduino_service):
        """Prueba de lectura de sensores"""
        self.stdout.write('\n3. PRUEBA DE SENSORES')
        self.stdout.write('-' * 20)
        
        # Simular algunos sensores para la prueba
        test_sensors = [
            {'pin': 'A0', 'tipo': 'humedad_suelo'},
            {'pin': 'A1', 'tipo': 'temperatura'},
            {'pin': 'A2', 'tipo': 'humedad_ambiente'},
        ]
        
        for sensor in test_sensors:
            try:
                value = arduino_service.read_sensor(sensor['pin'], sensor['tipo'])
                if value is not None:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'✓ Sensor {sensor["tipo"]} (Pin {sensor["pin"]}): {value}'
                        )
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f'⚠ Sensor {sensor["tipo"]} (Pin {sensor["pin"]}): Sin datos'
                        )
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'✗ Error leyendo sensor {sensor["tipo"]}: {str(e)}'
                    )
                )

    def _test_actuators(self, arduino_service):
        """Prueba de control de actuadores"""
        self.stdout.write('\n4. PRUEBA DE ACTUADORES')
        self.stdout.write('-' * 20)
        
        # Simular algunos actuadores para la prueba
        test_actuators = [
            {'pin': 2, 'tipo': 'valvula_riego'},
            {'pin': 3, 'tipo': 'bomba_agua'},
            {'pin': 4, 'tipo': 'bomba_abono'},
        ]
        
        for actuator in test_actuators:
            try:
                # Encender actuador
                success = arduino_service.control_actuator(
                    actuator['pin'], True, actuator['tipo']
                )
                if success:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'✓ Actuador {actuator["tipo"]} (Pin {actuator["pin"]}): Encendido'
                        )
                    )
                    
                    # Esperar un momento
                    time.sleep(1)
                    
                    # Apagar actuador
                    success = arduino_service.control_actuator(
                        actuator['pin'], False, actuator['tipo']
                    )
                    if success:
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'✓ Actuador {actuator["tipo"]} (Pin {actuator["pin"]}): Apagado'
                            )
                        )
                    else:
                        self.stdout.write(
                            self.style.WARNING(
                                f'⚠ No se pudo apagar actuador {actuator["tipo"]}'
                            )
                        )
                else:
                    self.stdout.write(
                        self.style.ERROR(
                            f'✗ No se pudo encender actuador {actuator["tipo"]}'
                        )
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'✗ Error controlando actuador {actuator["tipo"]}: {str(e)}'
                    )
                )

    def _test_all(self, arduino_service):
        """Ejecuta todas las pruebas"""
        self._test_ping(arduino_service)
        self._test_status(arduino_service)
        self._test_sensors(arduino_service)
        self._test_actuators(arduino_service)
        
        # Mostrar estadísticas finales
        self.stdout.write('\n5. ESTADÍSTICAS')
        self.stdout.write('-' * 20)
        
        # Obtener estado actual del Arduino
        try:
            estado = EstadoArduino.objects.first()
            if estado:
                self.stdout.write(f'Estado de conexión: {"Conectado" if estado.conectado else "Desconectado"}')
                self.stdout.write(f'Comandos ejecutados: {estado.comandos_ejecutados}')
                self.stdout.write(f'Errores de comunicación: {estado.errores_comunicacion}')
                if estado.ultimo_ping:
                    self.stdout.write(f'Último ping: {estado.ultimo_ping}')
            
            # Mostrar últimos logs
            logs = LogComunicacion.objects.order_by('-timestamp')[:5]
            if logs:
                self.stdout.write('\nÚltimos logs:')
                for log in logs:
                    self.stdout.write(f'  {log.timestamp}: {log.get_tipo_display()} - {log.mensaje}')
                    
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'No se pudieron obtener estadísticas: {str(e)}')
            )
