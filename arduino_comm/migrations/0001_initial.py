# Generated by Django 4.2.7 on 2025-09-20 14:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ComandoArduino',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo_comando', models.CharField(choices=[('read_sensor', 'Leer Sensor'), ('activate_actuator', 'Activar Actuador'), ('deactivate_actuator', 'Desactivar Actuador'), ('get_status', 'Obtener Estado'), ('set_config', 'Configurar'), ('ping', 'Ping'), ('reset', 'Reset')], max_length=20)),
                ('comando', models.TextField(help_text='Comando completo enviado al Arduino')),
                ('parametros', models.JSONField(blank=True, default=dict)),
                ('estado', models.CharField(choices=[('pendiente', 'Pendiente'), ('enviado', 'Enviado'), ('ejecutado', 'Ejecutado'), ('error', 'Error'), ('timeout', 'Timeout')], default='pendiente', max_length=20)),
                ('respuesta', models.TextField(blank=True)),
                ('error_mensaje', models.TextField(blank=True)),
                ('tiempo_envio', models.DateTimeField(blank=True, null=True)),
                ('tiempo_respuesta', models.DateTimeField(blank=True, null=True)),
                ('intentos', models.IntegerField(default=0)),
                ('max_intentos', models.IntegerField(default=3)),
                ('prioridad', models.IntegerField(default=5, help_text='1=Alta, 10=Baja')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Comando Arduino',
                'verbose_name_plural': 'Comandos Arduino',
                'ordering': ['prioridad', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ConfiguracionArduino',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('puerto_serie', models.CharField(default='/dev/ttyUSB0', max_length=50)),
                ('baudrate', models.IntegerField(default=9600)),
                ('timeout_segundos', models.IntegerField(default=5)),
                ('reintentos_conexion', models.IntegerField(default=3)),
                ('intervalo_ping_segundos', models.IntegerField(default=30)),
                ('buffer_size', models.IntegerField(default=1024)),
                ('auto_reconectar', models.BooleanField(default=True)),
                ('debug_mode', models.BooleanField(default=False)),
                ('activa', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Configuración Arduino',
                'verbose_name_plural': 'Configuraciones Arduino',
            },
        ),
        migrations.CreateModel(
            name='EstadoArduino',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('conectado', models.BooleanField(default=False)),
                ('puerto_serie', models.CharField(blank=True, max_length=50)),
                ('baudrate', models.IntegerField(default=9600)),
                ('version_firmware', models.CharField(blank=True, max_length=50)),
                ('uptime_segundos', models.BigIntegerField(default=0)),
                ('memoria_libre', models.IntegerField(default=0)),
                ('temperatura_cpu', models.FloatField(blank=True, null=True)),
                ('voltaje_alimentacion', models.FloatField(blank=True, null=True)),
                ('ultimo_ping', models.DateTimeField(blank=True, null=True)),
                ('errores_comunicacion', models.IntegerField(default=0)),
                ('comandos_ejecutados', models.IntegerField(default=0)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Estado Arduino',
                'verbose_name_plural': 'Estados Arduino',
            },
        ),
        migrations.CreateModel(
            name='LogComunicacion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo', models.CharField(choices=[('enviado', 'Mensaje Enviado'), ('recibido', 'Mensaje Recibido'), ('error', 'Error'), ('conexion', 'Conexión'), ('desconexion', 'Desconexión')], max_length=20)),
                ('mensaje', models.TextField()),
                ('datos_raw', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('puerto_serie', models.CharField(blank=True, max_length=50)),
                ('comando_relacionado', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='arduino_comm.comandoarduino')),
            ],
            options={
                'verbose_name': 'Log de Comunicación',
                'verbose_name_plural': 'Logs de Comunicación',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['-timestamp'], name='arduino_com_timesta_884c63_idx'), models.Index(fields=['tipo', '-timestamp'], name='arduino_com_tipo_1e6ecb_idx')],
            },
        ),
    ]
