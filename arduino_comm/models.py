from django.db import models
from django.utils import timezone


class ComandoArduino(models.Model):
    """Modelo para comandos enviados al Arduino"""
    TIPO_COMANDO_CHOICES = [
        ('read_sensor', 'Leer Sensor'),
        ('activate_actuator', 'Activar Actuador'),
        ('deactivate_actuator', 'Desactivar Actuador'),
        ('get_status', 'Obtener Estado'),
        ('set_config', 'Configurar'),
        ('ping', 'Ping'),
        ('reset', 'Reset'),
    ]

    ESTADO_CHOICES = [
        ('pendiente', 'Pendiente'),
        ('enviado', 'Enviado'),
        ('ejecutado', 'Ejecutado'),
        ('error', 'Error'),
        ('timeout', 'Timeout'),
    ]

    tipo_comando = models.CharField(max_length=20, choices=TIPO_COMANDO_CHOICES)
    comando = models.TextField(help_text="Comando completo enviado al Arduino")
    parametros = models.JSONField(default=dict, blank=True)
    estado = models.CharField(max_length=20, choices=ESTADO_CHOICES, default='pendiente')
    respuesta = models.TextField(blank=True)
    error_mensaje = models.TextField(blank=True)
    tiempo_envio = models.DateTimeField(null=True, blank=True)
    tiempo_respuesta = models.DateTimeField(null=True, blank=True)
    intentos = models.IntegerField(default=0)
    max_intentos = models.IntegerField(default=3)
    prioridad = models.IntegerField(default=5, help_text="1=Alta, 10=Baja")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Comando Arduino"
        verbose_name_plural = "Comandos Arduino"
        ordering = ['prioridad', '-created_at']

    def __str__(self):
        return f"{self.get_tipo_comando_display()} - {self.estado}"

    def tiempo_respuesta_ms(self):
        """Calcula el tiempo de respuesta en milisegundos"""
        if self.tiempo_envio and self.tiempo_respuesta:
            delta = self.tiempo_respuesta - self.tiempo_envio
            return delta.total_seconds() * 1000
        return None


class EstadoArduino(models.Model):
    """Modelo para el estado actual del Arduino"""
    conectado = models.BooleanField(default=False)
    puerto_serie = models.CharField(max_length=50, blank=True)
    baudrate = models.IntegerField(default=9600)
    version_firmware = models.CharField(max_length=50, blank=True)
    uptime_segundos = models.BigIntegerField(default=0)
    memoria_libre = models.IntegerField(default=0)
    temperatura_cpu = models.FloatField(null=True, blank=True)
    voltaje_alimentacion = models.FloatField(null=True, blank=True)
    ultimo_ping = models.DateTimeField(null=True, blank=True)
    errores_comunicacion = models.IntegerField(default=0)
    comandos_ejecutados = models.IntegerField(default=0)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Estado Arduino"
        verbose_name_plural = "Estados Arduino"

    def __str__(self):
        estado = "Conectado" if self.conectado else "Desconectado"
        return f"Arduino {estado} - {self.puerto_serie}"

    def esta_online(self):
        """Verifica si el Arduino está online basado en el último ping"""
        if not self.ultimo_ping:
            return False
        tiempo_limite = timezone.now() - timezone.timedelta(minutes=5)
        return self.ultimo_ping > tiempo_limite


class LogComunicacion(models.Model):
    """Modelo para logs de comunicación con Arduino"""
    TIPO_LOG_CHOICES = [
        ('enviado', 'Mensaje Enviado'),
        ('recibido', 'Mensaje Recibido'),
        ('error', 'Error'),
        ('conexion', 'Conexión'),
        ('desconexion', 'Desconexión'),
    ]

    tipo = models.CharField(max_length=20, choices=TIPO_LOG_CHOICES)
    mensaje = models.TextField()
    datos_raw = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    puerto_serie = models.CharField(max_length=50, blank=True)
    comando_relacionado = models.ForeignKey(
        ComandoArduino,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = "Log de Comunicación"
        verbose_name_plural = "Logs de Comunicación"
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['-timestamp']),
            models.Index(fields=['tipo', '-timestamp']),
        ]

    def __str__(self):
        return f"{self.get_tipo_display()} - {self.timestamp}"


class ConfiguracionArduino(models.Model):
    """Configuración específica para la comunicación con Arduino"""
    puerto_serie = models.CharField(max_length=50, default='/dev/ttyUSB0')
    baudrate = models.IntegerField(default=9600)
    timeout_segundos = models.IntegerField(default=5)
    reintentos_conexion = models.IntegerField(default=3)
    intervalo_ping_segundos = models.IntegerField(default=30)
    buffer_size = models.IntegerField(default=1024)
    auto_reconectar = models.BooleanField(default=True)
    debug_mode = models.BooleanField(default=False)
    activa = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Configuración Arduino"
        verbose_name_plural = "Configuraciones Arduino"

    def __str__(self):
        return f"Config Arduino - {self.puerto_serie}@{self.baudrate}"
