import serial
import time
import json
import logging
import random
from datetime import datetime
from django.utils import timezone
from django.conf import settings
from .models import ComandoArduino, EstadoArduino, LogComunicacion, ConfiguracionArduino

logger = logging.getLogger('arduino_comm')


class ArduinoService:
    """Servicio para comunicación con Arduino"""
    
    def __init__(self, mock_mode=False):
        self.mock_mode = mock_mode
        self.connection = None
        self.config = self._get_config()
        
        if not mock_mode:
            self._connect()
        
        # Actualizar o crear estado del Arduino
        self.estado, created = EstadoArduino.objects.get_or_create(
            defaults={
                'puerto_serie': self.config.puerto_serie,
                'baudrate': self.config.baudrate,
                'conectado': not mock_mode and self.connection is not None
            }
        )
        
        if not created:
            self.estado.conectado = not mock_mode and self.connection is not None
            self.estado.puerto_serie = self.config.puerto_serie
            self.estado.baudrate = self.config.baudrate
            self.estado.save()

    def _get_config(self):
        """Obtiene la configuración activa del Arduino"""
        try:
            return ConfiguracionArduino.objects.filter(activa=True).first()
        except ConfiguracionArduino.DoesNotExist:
            # Crear configuración por defecto
            return ConfiguracionArduino.objects.create(
                puerto_serie=getattr(settings, 'ARDUINO_PORT', '/dev/ttyUSB0'),
                baudrate=getattr(settings, 'ARDUINO_BAUDRATE', 9600),
                timeout_segundos=getattr(settings, 'ARDUINO_TIMEOUT', 5),
                activa=True
            )

    def _connect(self):
        """Establece conexión con Arduino"""
        try:
            self.connection = serial.Serial(
                port=self.config.puerto_serie,
                baudrate=self.config.baudrate,
                timeout=self.config.timeout_segundos
            )
            time.sleep(2)  # Esperar a que Arduino se inicialice
            
            self._log_communication('conexion', f'Conectado a {self.config.puerto_serie}')
            logger.info(f'Conectado a Arduino en {self.config.puerto_serie}')
            
        except Exception as e:
            self.connection = None
            self._log_communication('error', f'Error de conexión: {str(e)}')
            logger.error(f'Error conectando a Arduino: {str(e)}')
            raise

    def _send_command(self, command, wait_response=True):
        """Envía un comando al Arduino"""
        if self.mock_mode:
            return self._mock_response(command)
        
        if not self.connection:
            raise Exception("No hay conexión con Arduino")
        
        try:
            # Enviar comando
            self.connection.write(f"{command}\n".encode())
            self._log_communication('enviado', command)
            
            if wait_response:
                # Esperar respuesta
                response = self.connection.readline().decode().strip()
                self._log_communication('recibido', response)
                return response
            
            return True
            
        except Exception as e:
            self._log_communication('error', f'Error enviando comando: {str(e)}')
            logger.error(f'Error enviando comando {command}: {str(e)}')
            raise

    def _mock_response(self, command):
        """Genera respuestas simuladas para pruebas"""
        time.sleep(0.1)  # Simular latencia
        
        if command.startswith('PING'):
            return 'PONG'
        elif command.startswith('STATUS'):
            return json.dumps({
                'uptime': random.randint(1000, 10000),
                'memory': random.randint(1000, 2000),
                'temperature': round(random.uniform(20, 35), 1),
                'voltage': round(random.uniform(4.8, 5.2), 2)
            })
        elif command.startswith('READ_SENSOR'):
            # Simular lectura de sensor
            return str(round(random.uniform(0, 100), 2))
        elif command.startswith('SET_ACTUATOR'):
            return 'OK'
        else:
            return 'UNKNOWN_COMMAND'

    def _log_communication(self, tipo, mensaje, comando=None):
        """Registra comunicación en la base de datos"""
        try:
            LogComunicacion.objects.create(
                tipo=tipo,
                mensaje=mensaje,
                puerto_serie=self.config.puerto_serie,
                comando_relacionado=comando
            )
        except Exception as e:
            logger.error(f'Error guardando log: {str(e)}')

    def ping(self):
        """Envía ping al Arduino"""
        try:
            response = self._send_command('PING')
            
            # Actualizar último ping
            self.estado.ultimo_ping = timezone.now()
            self.estado.save()
            
            return response == 'PONG'
            
        except Exception as e:
            logger.error(f'Error en ping: {str(e)}')
            return False

    def get_system_status(self):
        """Obtiene el estado del sistema Arduino"""
        try:
            response = self._send_command('STATUS')
            
            if self.mock_mode or response:
                if self.mock_mode:
                    status_data = json.loads(response)
                else:
                    status_data = json.loads(response)
                
                # Actualizar estado en BD
                self.estado.uptime_segundos = status_data.get('uptime', 0)
                self.estado.memoria_libre = status_data.get('memory', 0)
                self.estado.temperatura_cpu = status_data.get('temperature')
                self.estado.voltaje_alimentacion = status_data.get('voltage')
                self.estado.save()
                
                return status_data
            
            return None
            
        except Exception as e:
            logger.error(f'Error obteniendo estado: {str(e)}')
            return None

    def read_sensor(self, pin, sensor_type):
        """Lee un sensor específico"""
        try:
            command = f'READ_SENSOR:{pin}:{sensor_type}'
            response = self._send_command(command)
            
            if response and response != 'ERROR':
                return float(response)
            
            return None
            
        except Exception as e:
            logger.error(f'Error leyendo sensor {pin}: {str(e)}')
            return None

    def control_actuator(self, pin, state, actuator_type):
        """Controla un actuador"""
        try:
            state_str = 'ON' if state else 'OFF'
            command = f'SET_ACTUATOR:{pin}:{state_str}:{actuator_type}'
            response = self._send_command(command)
            
            success = response == 'OK'
            
            if success:
                self.estado.comandos_ejecutados += 1
                self.estado.save()
            else:
                self.estado.errores_comunicacion += 1
                self.estado.save()
            
            return success
            
        except Exception as e:
            logger.error(f'Error controlando actuador {pin}: {str(e)}')
            self.estado.errores_comunicacion += 1
            self.estado.save()
            return False

    def execute_command(self, comando_obj):
        """Ejecuta un comando desde la base de datos"""
        try:
            comando_obj.estado = 'enviado'
            comando_obj.tiempo_envio = timezone.now()
            comando_obj.intentos += 1
            comando_obj.save()
            
            response = self._send_command(comando_obj.comando)
            
            comando_obj.estado = 'ejecutado'
            comando_obj.respuesta = response
            comando_obj.tiempo_respuesta = timezone.now()
            comando_obj.save()
            
            return True
            
        except Exception as e:
            comando_obj.estado = 'error'
            comando_obj.error_mensaje = str(e)
            comando_obj.save()
            
            logger.error(f'Error ejecutando comando {comando_obj.id}: {str(e)}')
            return False

    def close(self):
        """Cierra la conexión con Arduino"""
        if self.connection and not self.mock_mode:
            try:
                self.connection.close()
                self._log_communication('desconexion', 'Conexión cerrada')
                logger.info('Conexión con Arduino cerrada')
            except Exception as e:
                logger.error(f'Error cerrando conexión: {str(e)}')
        
        # Actualizar estado
        self.estado.conectado = False
        self.estado.save()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
