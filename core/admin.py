from django.contrib import admin
from .models import (
    Sector, Sensor, Actuador, LecturaSensor,
    EventoActuador, ProgramaRiego, ConfiguracionSistema
)


@admin.register(Sector)
class SectorAdmin(admin.ModelAdmin):
    list_display = ['nombre', 'tipo_cultivo', 'area_m2', 'activo', 'pin_valvula', 'tiempo_riego_minutos']
    list_filter = ['activo', 'tipo_cultivo']
    search_fields = ['nombre', 'tipo_cultivo']
    ordering = ['nombre']


@admin.register(Sensor)
class SensorAdmin(admin.ModelAdmin):
    list_display = ['nombre', 'tipo', 'sector', 'pin_arduino', 'activo', 'unidad_medida']
    list_filter = ['tipo', 'activo', 'sector']
    search_fields = ['nombre']
    ordering = ['sector', 'nombre']


@admin.register(Actuador)
class ActuadorAdmin(admin.ModelAdmin):
    list_display = ['nombre', 'tipo', 'sector', 'pin_arduino', 'activo', 'estado_actual']
    list_filter = ['tipo', 'activo', 'estado_actual', 'sector']
    search_fields = ['nombre']
    ordering = ['sector', 'nombre']


@admin.register(LecturaSensor)
class LecturaSensorAdmin(admin.ModelAdmin):
    list_display = ['sensor', 'valor', 'valor_calibrado', 'timestamp']
    list_filter = ['sensor', 'timestamp']
    search_fields = ['sensor__nombre']
    ordering = ['-timestamp']
    readonly_fields = ['timestamp', 'valor_calibrado']


@admin.register(EventoActuador)
class EventoActuadorAdmin(admin.ModelAdmin):
    list_display = ['actuador', 'tipo_evento', 'timestamp', 'usuario', 'automatico', 'duracion_minutos']
    list_filter = ['tipo_evento', 'automatico', 'timestamp']
    search_fields = ['actuador__nombre', 'descripcion']
    ordering = ['-timestamp']
    readonly_fields = ['timestamp']


@admin.register(ProgramaRiego)
class ProgramaRiegoAdmin(admin.ModelAdmin):
    list_display = ['nombre', 'sector', 'hora_inicio', 'duracion_minutos', 'activo']
    list_filter = ['activo', 'sector', 'usar_sensor_humedad']
    search_fields = ['nombre', 'sector__nombre']
    ordering = ['sector', 'hora_inicio']


@admin.register(ConfiguracionSistema)
class ConfiguracionSistemaAdmin(admin.ModelAdmin):
    list_display = ['clave', 'valor', 'tipo_dato', 'updated_at', 'updated_by']
    list_filter = ['tipo_dato']
    search_fields = ['clave', 'descripcion']
    ordering = ['clave']
