# Generated by Django 4.2.7 on 2025-09-20 14:19

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Actuador',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=100)),
                ('tipo', models.CharField(choices=[('valvula_riego', 'Válvula de Riego'), ('bomba_agua', 'Bomba de Agua'), ('bomba_abono', 'Bomba de Abono'), ('ventilador', 'Ventilador'), ('calefactor', 'Calefactor'), ('luz_led', 'Luz LED')], max_length=20)),
                ('pin_arduino', models.IntegerField(help_text='Pin digital del Arduino', validators=[django.core.validators.MinValueValidator(2), django.core.validators.MaxValueValidator(53)])),
                ('activo', models.BooleanField(default=True)),
                ('estado_actual', models.BooleanField(default=False, help_text='True=Encendido, False=Apagado')),
                ('potencia_watts', models.FloatField(default=0.0, help_text='Potencia en watts', validators=[django.core.validators.MinValueValidator(0)])),
                ('tiempo_activacion_max', models.IntegerField(default=60, help_text='Tiempo máximo de activación en minutos', validators=[django.core.validators.MinValueValidator(1)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Actuador',
                'verbose_name_plural': 'Actuadores',
            },
        ),
        migrations.CreateModel(
            name='Sector',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=100, unique=True)),
                ('descripcion', models.TextField(blank=True)),
                ('area_m2', models.FloatField(help_text='Área del sector en metros cuadrados', validators=[django.core.validators.MinValueValidator(0.1)])),
                ('tipo_cultivo', models.CharField(blank=True, max_length=100)),
                ('activo', models.BooleanField(default=True)),
                ('pin_valvula', models.IntegerField(help_text='Pin del Arduino para controlar la válvula', unique=True, validators=[django.core.validators.MinValueValidator(2), django.core.validators.MaxValueValidator(53)])),
                ('tiempo_riego_minutos', models.IntegerField(default=30, help_text='Tiempo de riego por defecto en minutos', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(480)])),
                ('humedad_minima', models.FloatField(default=30.0, help_text='Humedad mínima para activar riego (%)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Sector',
                'verbose_name_plural': 'Sectores',
                'ordering': ['nombre'],
            },
        ),
        migrations.CreateModel(
            name='Sensor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=100)),
                ('tipo', models.CharField(choices=[('humedad_suelo', 'Humedad del Suelo'), ('temperatura', 'Temperatura'), ('humedad_ambiente', 'Humedad Ambiente'), ('ph', 'pH del Suelo'), ('luz', 'Sensor de Luz'), ('presion', 'Presión de Agua')], max_length=20)),
                ('pin_arduino', models.IntegerField(help_text='Pin analógico o digital del Arduino', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(53)])),
                ('activo', models.BooleanField(default=True)),
                ('valor_minimo', models.FloatField(default=0.0)),
                ('valor_maximo', models.FloatField(default=100.0)),
                ('unidad_medida', models.CharField(default='%', max_length=20)),
                ('calibracion_offset', models.FloatField(default=0.0)),
                ('calibracion_factor', models.FloatField(default=1.0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sector', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sensores', to='core.sector')),
            ],
            options={
                'verbose_name': 'Sensor',
                'verbose_name_plural': 'Sensores',
                'unique_together': {('pin_arduino', 'tipo')},
            },
        ),
        migrations.CreateModel(
            name='ProgramaRiego',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=100)),
                ('activo', models.BooleanField(default=True)),
                ('hora_inicio', models.TimeField()),
                ('duracion_minutos', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(480)])),
                ('dias_semana', models.JSONField(default=list, help_text='Lista de días de la semana')),
                ('usar_sensor_humedad', models.BooleanField(default=True)),
                ('humedad_minima_activacion', models.FloatField(default=30.0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sector', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='programas_riego', to='core.sector')),
            ],
            options={
                'verbose_name': 'Programa de Riego',
                'verbose_name_plural': 'Programas de Riego',
                'ordering': ['sector', 'hora_inicio'],
            },
        ),
        migrations.CreateModel(
            name='EventoActuador',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo_evento', models.CharField(choices=[('encendido', 'Encendido'), ('apagado', 'Apagado'), ('error', 'Error'), ('mantenimiento', 'Mantenimiento')], max_length=20)),
                ('descripcion', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('duracion_minutos', models.IntegerField(blank=True, null=True)),
                ('automatico', models.BooleanField(default=True, help_text='True si fue activado automáticamente')),
                ('actuador', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='eventos', to='core.actuador')),
                ('usuario', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Evento de Actuador',
                'verbose_name_plural': 'Eventos de Actuadores',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='ConfiguracionSistema',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('clave', models.CharField(max_length=100, unique=True)),
                ('valor', models.TextField()),
                ('descripcion', models.TextField(blank=True)),
                ('tipo_dato', models.CharField(choices=[('string', 'Texto'), ('integer', 'Entero'), ('float', 'Decimal'), ('boolean', 'Booleano'), ('json', 'JSON')], default='string', max_length=20)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Configuración del Sistema',
                'verbose_name_plural': 'Configuraciones del Sistema',
            },
        ),
        migrations.AddField(
            model_name='actuador',
            name='sector',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='actuadores', to='core.sector'),
        ),
        migrations.CreateModel(
            name='LecturaSensor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('valor', models.FloatField()),
                ('valor_calibrado', models.FloatField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('sensor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lecturas', to='core.sensor')),
            ],
            options={
                'verbose_name': 'Lectura de Sensor',
                'verbose_name_plural': 'Lecturas de Sensores',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['sensor', '-timestamp'], name='core_lectur_sensor__b8fd64_idx'), models.Index(fields=['-timestamp'], name='core_lectur_timesta_45e6ec_idx')],
            },
        ),
        migrations.AlterUniqueTogether(
            name='actuador',
            unique_together={('pin_arduino',)},
        ),
    ]
