from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone


class Sector(models.Model):
    """Modelo para representar sectores de riego"""
    nombre = models.CharField(max_length=100, unique=True)
    descripcion = models.TextField(blank=True)
    area_m2 = models.FloatField(
        validators=[MinValueValidator(0.1)],
        help_text="Área del sector en metros cuadrados"
    )
    tipo_cultivo = models.CharField(max_length=100, blank=True)
    activo = models.BooleanField(default=True)
    pin_valvula = models.IntegerField(
        unique=True,
        validators=[MinValueValidator(2), MaxValueValidator(53)],
        help_text="Pin del Arduino para controlar la válvula"
    )
    tiempo_riego_minutos = models.IntegerField(
        default=30,
        validators=[MinValueValidator(1), MaxValueValidator(480)],
        help_text="Tiempo de riego por defecto en minutos"
    )
    humedad_minima = models.FloatField(
        default=30.0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Humedad mínima para activar riego (%)"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Sector"
        verbose_name_plural = "Sectores"
        ordering = ['nombre']

    def __str__(self):
        return f"{self.nombre} - {self.tipo_cultivo}"


class Sensor(models.Model):
    """Modelo para sensores del sistema"""
    TIPO_SENSOR_CHOICES = [
        ('humedad_suelo', 'Humedad del Suelo'),
        ('temperatura', 'Temperatura'),
        ('humedad_ambiente', 'Humedad Ambiente'),
        ('ph', 'pH del Suelo'),
        ('luz', 'Sensor de Luz'),
        ('presion', 'Presión de Agua'),
    ]

    nombre = models.CharField(max_length=100)
    tipo = models.CharField(max_length=20, choices=TIPO_SENSOR_CHOICES)
    sector = models.ForeignKey(
        Sector,
        on_delete=models.CASCADE,
        related_name='sensores',
        null=True,
        blank=True
    )
    pin_arduino = models.IntegerField(
        validators=[MinValueValidator(0), MaxValueValidator(53)],
        help_text="Pin analógico o digital del Arduino"
    )
    activo = models.BooleanField(default=True)
    valor_minimo = models.FloatField(default=0.0)
    valor_maximo = models.FloatField(default=100.0)
    unidad_medida = models.CharField(max_length=20, default='%')
    calibracion_offset = models.FloatField(default=0.0)
    calibracion_factor = models.FloatField(default=1.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Sensor"
        verbose_name_plural = "Sensores"
        unique_together = ['pin_arduino', 'tipo']

    def __str__(self):
        return f"{self.nombre} ({self.get_tipo_display()})"


class Actuador(models.Model):
    """Modelo para actuadores del sistema"""
    TIPO_ACTUADOR_CHOICES = [
        ('valvula_riego', 'Válvula de Riego'),
        ('bomba_agua', 'Bomba de Agua'),
        ('bomba_abono', 'Bomba de Abono'),
        ('ventilador', 'Ventilador'),
        ('calefactor', 'Calefactor'),
        ('luz_led', 'Luz LED'),
    ]

    nombre = models.CharField(max_length=100)
    tipo = models.CharField(max_length=20, choices=TIPO_ACTUADOR_CHOICES)
    sector = models.ForeignKey(
        Sector,
        on_delete=models.CASCADE,
        related_name='actuadores',
        null=True,
        blank=True
    )
    pin_arduino = models.IntegerField(
        validators=[MinValueValidator(2), MaxValueValidator(53)],
        help_text="Pin digital del Arduino"
    )
    activo = models.BooleanField(default=True)
    estado_actual = models.BooleanField(default=False, help_text="True=Encendido, False=Apagado")
    potencia_watts = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0)],
        help_text="Potencia en watts"
    )
    tiempo_activacion_max = models.IntegerField(
        default=60,
        validators=[MinValueValidator(1)],
        help_text="Tiempo máximo de activación en minutos"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Actuador"
        verbose_name_plural = "Actuadores"
        unique_together = ['pin_arduino']

    def __str__(self):
        return f"{self.nombre} ({self.get_tipo_display()})"


class LecturaSensor(models.Model):
    """Modelo para almacenar lecturas de sensores"""
    sensor = models.ForeignKey(Sensor, on_delete=models.CASCADE, related_name='lecturas')
    valor = models.FloatField()
    valor_calibrado = models.FloatField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Lectura de Sensor"
        verbose_name_plural = "Lecturas de Sensores"
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['sensor', '-timestamp']),
            models.Index(fields=['-timestamp']),
        ]

    def save(self, *args, **kwargs):
        # Aplicar calibración automáticamente
        if self.sensor:
            self.valor_calibrado = (self.valor + self.sensor.calibracion_offset) * self.sensor.calibracion_factor
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.sensor.nombre}: {self.valor_calibrado} {self.sensor.unidad_medida}"


class EventoActuador(models.Model):
    """Modelo para registrar eventos de actuadores"""
    TIPO_EVENTO_CHOICES = [
        ('encendido', 'Encendido'),
        ('apagado', 'Apagado'),
        ('error', 'Error'),
        ('mantenimiento', 'Mantenimiento'),
    ]

    actuador = models.ForeignKey(Actuador, on_delete=models.CASCADE, related_name='eventos')
    tipo_evento = models.CharField(max_length=20, choices=TIPO_EVENTO_CHOICES)
    descripcion = models.TextField(blank=True)
    usuario = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    duracion_minutos = models.IntegerField(null=True, blank=True)
    automatico = models.BooleanField(default=True, help_text="True si fue activado automáticamente")

    class Meta:
        verbose_name = "Evento de Actuador"
        verbose_name_plural = "Eventos de Actuadores"
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.actuador.nombre} - {self.get_tipo_evento_display()} ({self.timestamp})"


class ProgramaRiego(models.Model):
    """Modelo para programar riegos automáticos"""
    DIAS_SEMANA_CHOICES = [
        ('lunes', 'Lunes'),
        ('martes', 'Martes'),
        ('miercoles', 'Miércoles'),
        ('jueves', 'Jueves'),
        ('viernes', 'Viernes'),
        ('sabado', 'Sábado'),
        ('domingo', 'Domingo'),
    ]

    nombre = models.CharField(max_length=100)
    sector = models.ForeignKey(Sector, on_delete=models.CASCADE, related_name='programas_riego')
    activo = models.BooleanField(default=True)
    hora_inicio = models.TimeField()
    duracion_minutos = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(480)])
    dias_semana = models.JSONField(default=list, help_text="Lista de días de la semana")
    usar_sensor_humedad = models.BooleanField(default=True)
    humedad_minima_activacion = models.FloatField(
        default=30.0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Programa de Riego"
        verbose_name_plural = "Programas de Riego"
        ordering = ['sector', 'hora_inicio']

    def __str__(self):
        return f"{self.nombre} - {self.sector.nombre}"


class ConfiguracionSistema(models.Model):
    """Modelo para configuraciones globales del sistema"""
    clave = models.CharField(max_length=100, unique=True)
    valor = models.TextField()
    descripcion = models.TextField(blank=True)
    tipo_dato = models.CharField(
        max_length=20,
        choices=[
            ('string', 'Texto'),
            ('integer', 'Entero'),
            ('float', 'Decimal'),
            ('boolean', 'Booleano'),
            ('json', 'JSON'),
        ],
        default='string'
    )
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name = "Configuración del Sistema"
        verbose_name_plural = "Configuraciones del Sistema"

    def __str__(self):
        return f"{self.clave}: {self.valor}"

    def get_valor_typed(self):
        """Devuelve el valor convertido al tipo correcto"""
        if self.tipo_dato == 'integer':
            return int(self.valor)
        elif self.tipo_dato == 'float':
            return float(self.valor)
        elif self.tipo_dato == 'boolean':
            return self.valor.lower() in ['true', '1', 'yes', 'on']
        elif self.tipo_dato == 'json':
            import json
            return json.loads(self.valor)
        return self.valor
