from rest_framework import serializers
from .models import (
    Sector, Sensor, Actuador, LecturaSensor, 
    EventoActuador, ProgramaRiego, ConfiguracionSistema
)


class SectorSerializer(serializers.ModelSerializer):
    """Serializer para Sectores"""
    sensores_count = serializers.SerializerMethodField()
    actuadores_count = serializers.SerializerMethodField()
    ultima_lectura_humedad = serializers.SerializerMethodField()
    
    class Meta:
        model = Sector
        fields = [
            'id', 'nombre', 'descripcion', 'area_m2', 'tipo_cultivo',
            'activo', 'pin_valvula', 'tiempo_riego_minutos', 'humedad_minima',
            'created_at', 'updated_at', 'sensores_count', 'actuadores_count',
            'ultima_lectura_humedad'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_sensores_count(self, obj):
        return obj.sensores.filter(activo=True).count()
    
    def get_actuadores_count(self, obj):
        return obj.actuadores.filter(activo=True).count()
    
    def get_ultima_lectura_humedad(self, obj):
        sensor_humedad = obj.sensores.filter(
            tipo='humedad_suelo', 
            activo=True
        ).first()
        
        if sensor_humedad:
            ultima_lectura = sensor_humedad.lecturas.first()
            if ultima_lectura:
                return {
                    'valor': ultima_lectura.valor_calibrado,
                    'timestamp': ultima_lectura.timestamp
                }
        return None


class SensorSerializer(serializers.ModelSerializer):
    """Serializer para Sensores"""
    sector_nombre = serializers.CharField(source='sector.nombre', read_only=True)
    ultima_lectura = serializers.SerializerMethodField()
    
    class Meta:
        model = Sensor
        fields = [
            'id', 'nombre', 'tipo', 'sector', 'sector_nombre', 'pin_arduino',
            'activo', 'valor_minimo', 'valor_maximo', 'unidad_medida',
            'calibracion_offset', 'calibracion_factor', 'created_at',
            'updated_at', 'ultima_lectura'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_ultima_lectura(self, obj):
        lectura = obj.lecturas.first()
        if lectura:
            return {
                'valor': lectura.valor,
                'valor_calibrado': lectura.valor_calibrado,
                'timestamp': lectura.timestamp
            }
        return None


class ActuadorSerializer(serializers.ModelSerializer):
    """Serializer para Actuadores"""
    sector_nombre = serializers.CharField(source='sector.nombre', read_only=True)
    ultimo_evento = serializers.SerializerMethodField()
    
    class Meta:
        model = Actuador
        fields = [
            'id', 'nombre', 'tipo', 'sector', 'sector_nombre', 'pin_arduino',
            'activo', 'estado_actual', 'potencia_watts', 'tiempo_activacion_max',
            'created_at', 'updated_at', 'ultimo_evento'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_ultimo_evento(self, obj):
        evento = obj.eventos.first()
        if evento:
            return {
                'tipo': evento.tipo_evento,
                'timestamp': evento.timestamp,
                'automatico': evento.automatico,
                'duracion_minutos': evento.duracion_minutos
            }
        return None


class LecturaSensorSerializer(serializers.ModelSerializer):
    """Serializer para Lecturas de Sensores"""
    sensor_nombre = serializers.CharField(source='sensor.nombre', read_only=True)
    sensor_tipo = serializers.CharField(source='sensor.get_tipo_display', read_only=True)
    unidad_medida = serializers.CharField(source='sensor.unidad_medida', read_only=True)
    
    class Meta:
        model = LecturaSensor
        fields = [
            'id', 'sensor', 'sensor_nombre', 'sensor_tipo', 'valor',
            'valor_calibrado', 'timestamp', 'unidad_medida'
        ]
        read_only_fields = ['timestamp', 'valor_calibrado']


class EventoActuadorSerializer(serializers.ModelSerializer):
    """Serializer para Eventos de Actuadores"""
    actuador_nombre = serializers.CharField(source='actuador.nombre', read_only=True)
    usuario_nombre = serializers.CharField(source='usuario.get_full_name', read_only=True)
    
    class Meta:
        model = EventoActuador
        fields = [
            'id', 'actuador', 'actuador_nombre', 'tipo_evento', 'descripcion',
            'usuario', 'usuario_nombre', 'timestamp', 'duracion_minutos', 'automatico'
        ]
        read_only_fields = ['timestamp']


class ProgramaRiegoSerializer(serializers.ModelSerializer):
    """Serializer para Programas de Riego"""
    sector_nombre = serializers.CharField(source='sector.nombre', read_only=True)
    
    class Meta:
        model = ProgramaRiego
        fields = [
            'id', 'nombre', 'sector', 'sector_nombre', 'activo', 'hora_inicio',
            'duracion_minutos', 'dias_semana', 'usar_sensor_humedad',
            'humedad_minima_activacion', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class ConfiguracionSistemaSerializer(serializers.ModelSerializer):
    """Serializer para Configuración del Sistema"""
    valor_typed = serializers.SerializerMethodField()
    updated_by_nombre = serializers.CharField(source='updated_by.get_full_name', read_only=True)
    
    class Meta:
        model = ConfiguracionSistema
        fields = [
            'id', 'clave', 'valor', 'valor_typed', 'descripcion', 'tipo_dato',
            'updated_at', 'updated_by', 'updated_by_nombre'
        ]
        read_only_fields = ['updated_at']
    
    def get_valor_typed(self, obj):
        return obj.get_valor_typed()


# Serializers para control de actuadores
class ControlActuadorSerializer(serializers.Serializer):
    """Serializer para controlar actuadores"""
    actuador_id = serializers.IntegerField()
    estado = serializers.BooleanField()
    duracion_minutos = serializers.IntegerField(required=False, min_value=1, max_value=480)
    descripcion = serializers.CharField(required=False, max_length=500)
    
    def validate_actuador_id(self, value):
        try:
            actuador = Actuador.objects.get(id=value, activo=True)
            return value
        except Actuador.DoesNotExist:
            raise serializers.ValidationError("Actuador no encontrado o inactivo")


class LeerSensorSerializer(serializers.Serializer):
    """Serializer para leer sensores"""
    sensor_id = serializers.IntegerField()
    
    def validate_sensor_id(self, value):
        try:
            sensor = Sensor.objects.get(id=value, activo=True)
            return value
        except Sensor.DoesNotExist:
            raise serializers.ValidationError("Sensor no encontrado o inactivo")


# Serializers para estadísticas y dashboard
class EstadisticasSectorSerializer(serializers.Serializer):
    """Serializer para estadísticas de sectores"""
    sector_id = serializers.IntegerField()
    sector_nombre = serializers.CharField()
    humedad_promedio = serializers.FloatField()
    temperatura_promedio = serializers.FloatField()
    riegos_hoy = serializers.IntegerField()
    tiempo_riego_total = serializers.IntegerField()
    estado_valvula = serializers.BooleanField()


class DashboardDataSerializer(serializers.Serializer):
    """Serializer para datos del dashboard"""
    sectores_activos = serializers.IntegerField()
    sensores_activos = serializers.IntegerField()
    actuadores_activos = serializers.IntegerField()
    lecturas_hoy = serializers.IntegerField()
    eventos_hoy = serializers.IntegerField()
    arduino_conectado = serializers.BooleanField()
    ultimo_ping = serializers.DateTimeField()
    alertas_activas = serializers.IntegerField()
