from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

app_name = 'core'

# Router para ViewSets
router = DefaultRouter()
router.register(r'sectores', views.SectorViewSet)
router.register(r'sensores', views.SensorViewSet)
router.register(r'actuadores', views.ActuadorViewSet)
router.register(r'programas-riego', views.ProgramaRiegoViewSet)
router.register(r'lecturas', views.LecturaSensorViewSet)

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),
    
    # Vistas específicas de API
    path('api/dashboard/', views.dashboard_data, name='dashboard_data'),
    path('api/estadisticas/', views.estadisticas_generales, name='estadisticas_generales'),
]
