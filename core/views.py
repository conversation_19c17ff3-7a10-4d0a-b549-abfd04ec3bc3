from django.shortcuts import render, get_object_or_404
from django.utils import timezone
from django.db.models import Avg, Count, Q
from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from datetime import datetime, timedelta
import json

from .models import (
    Sector, Sensor, Actuador, LecturaSensor,
    EventoActuador, ProgramaRiego, ConfiguracionSistema
)
from .serializers import (
    SectorSerializer, SensorSerializer, ActuadorSerializer,
    LecturaSensorSerializer, EventoActuadorSerializer,
    ProgramaRiegoSerializer, ConfiguracionSistemaSerializer,
    ControlActuadorSerializer, LeerSensorSerializer,
    EstadisticasSectorSerializer, DashboardDataSerializer
)
from arduino_comm.models import EstadoArduino, ComandoArduino
from arduino_comm.services import ArduinoService


class SectorViewSet(viewsets.ModelViewSet):
    """ViewSet para gestión de sectores"""
    queryset = Sector.objects.all()
    serializer_class = SectorSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = Sector.objects.all()
        activo = self.request.query_params.get('activo')
        if activo is not None:
            queryset = queryset.filter(activo=activo.lower() == 'true')
        return queryset.order_by('nombre')

    @action(detail=True, methods=['get'])
    def estadisticas(self, request, pk=None):
        """Obtiene estadísticas de un sector específico"""
        sector = self.get_object()
        hoy = timezone.now().date()

        # Obtener sensores del sector
        sensores = sector.sensores.filter(activo=True)

        # Calcular promedios de hoy
        lecturas_hoy = LecturaSensor.objects.filter(
            sensor__in=sensores,
            timestamp__date=hoy
        )

        humedad_promedio = lecturas_hoy.filter(
            sensor__tipo='humedad_suelo'
        ).aggregate(Avg('valor_calibrado'))['valor_calibrado__avg'] or 0

        temperatura_promedio = lecturas_hoy.filter(
            sensor__tipo='temperatura'
        ).aggregate(Avg('valor_calibrado'))['valor_calibrado__avg'] or 0

        # Eventos de riego de hoy
        eventos_riego = EventoActuador.objects.filter(
            actuador__sector=sector,
            actuador__tipo='valvula_riego',
            tipo_evento='encendido',
            timestamp__date=hoy
        )

        riegos_hoy = eventos_riego.count()
        tiempo_riego_total = sum([
            evento.duracion_minutos or 0 for evento in eventos_riego
        ])

        # Estado actual de la válvula
        valvula = sector.actuadores.filter(tipo='valvula_riego').first()
        estado_valvula = valvula.estado_actual if valvula else False

        data = {
            'sector_id': sector.id,
            'sector_nombre': sector.nombre,
            'humedad_promedio': round(humedad_promedio, 1),
            'temperatura_promedio': round(temperatura_promedio, 1),
            'riegos_hoy': riegos_hoy,
            'tiempo_riego_total': tiempo_riego_total,
            'estado_valvula': estado_valvula
        }

        serializer = EstadisticasSectorSerializer(data)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def activar_riego(self, request, pk=None):
        """Activa el riego manual de un sector"""
        sector = self.get_object()
        duracion = request.data.get('duracion_minutos', sector.tiempo_riego_minutos)

        # Buscar válvula del sector
        valvula = sector.actuadores.filter(
            tipo='valvula_riego',
            activo=True
        ).first()

        if not valvula:
            return Response(
                {'error': 'No se encontró válvula activa para este sector'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Crear comando para Arduino
            comando = ComandoArduino.objects.create(
                tipo_comando='activate_actuator',
                comando=f'SET_ACTUATOR:{valvula.pin_arduino}:ON:valvula_riego',
                parametros={
                    'actuador_id': valvula.id,
                    'duracion_minutos': duracion,
                    'sector_id': sector.id
                },
                prioridad=2  # Alta prioridad para riego manual
            )

            # Registrar evento
            EventoActuador.objects.create(
                actuador=valvula,
                tipo_evento='encendido',
                descripcion=f'Riego manual activado por {request.user.get_full_name()}',
                usuario=request.user,
                duracion_minutos=duracion,
                automatico=False
            )

            return Response({
                'message': f'Riego activado para {duracion} minutos',
                'comando_id': comando.id
            })

        except Exception as e:
            return Response(
                {'error': f'Error activando riego: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class SensorViewSet(viewsets.ModelViewSet):
    """ViewSet para gestión de sensores"""
    queryset = Sensor.objects.all()
    serializer_class = SensorSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = Sensor.objects.all()
        sector = self.request.query_params.get('sector')
        tipo = self.request.query_params.get('tipo')
        activo = self.request.query_params.get('activo')

        if sector:
            queryset = queryset.filter(sector_id=sector)
        if tipo:
            queryset = queryset.filter(tipo=tipo)
        if activo is not None:
            queryset = queryset.filter(activo=activo.lower() == 'true')

        return queryset.order_by('sector__nombre', 'nombre')

    @action(detail=True, methods=['post'])
    def leer_ahora(self, request, pk=None):
        """Fuerza una lectura inmediata del sensor"""
        sensor = self.get_object()

        try:
            # Crear comando para leer sensor
            comando = ComandoArduino.objects.create(
                tipo_comando='read_sensor',
                comando=f'READ_SENSOR:A{sensor.pin_arduino}:{sensor.tipo}',
                parametros={'sensor_id': sensor.id},
                prioridad=3
            )

            return Response({
                'message': 'Lectura solicitada',
                'comando_id': comando.id
            })

        except Exception as e:
            return Response(
                {'error': f'Error solicitando lectura: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def historial(self, request, pk=None):
        """Obtiene el historial de lecturas del sensor"""
        sensor = self.get_object()

        # Parámetros de consulta
        dias = int(request.query_params.get('dias', 7))
        limite = int(request.query_params.get('limite', 100))

        fecha_desde = timezone.now() - timedelta(days=dias)

        lecturas = LecturaSensor.objects.filter(
            sensor=sensor,
            timestamp__gte=fecha_desde
        ).order_by('-timestamp')[:limite]

        serializer = LecturaSensorSerializer(lecturas, many=True)
        return Response(serializer.data)


class ActuadorViewSet(viewsets.ModelViewSet):
    """ViewSet para gestión de actuadores"""
    queryset = Actuador.objects.all()
    serializer_class = ActuadorSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = Actuador.objects.all()
        sector = self.request.query_params.get('sector')
        tipo = self.request.query_params.get('tipo')
        activo = self.request.query_params.get('activo')

        if sector:
            queryset = queryset.filter(sector_id=sector)
        if tipo:
            queryset = queryset.filter(tipo=tipo)
        if activo is not None:
            queryset = queryset.filter(activo=activo.lower() == 'true')

        return queryset.order_by('sector__nombre', 'nombre')

    @action(detail=True, methods=['post'])
    def controlar(self, request, pk=None):
        """Controla el estado de un actuador"""
        actuador = self.get_object()

        serializer = ControlActuadorSerializer(data={
            'actuador_id': actuador.id,
            **request.data
        })

        if not serializer.is_valid():
            return Response(
                serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )

        estado = serializer.validated_data['estado']
        duracion = serializer.validated_data.get('duracion_minutos')
        descripcion = serializer.validated_data.get('descripcion', '')

        try:
            # Crear comando para Arduino
            estado_str = 'ON' if estado else 'OFF'
            comando = ComandoArduino.objects.create(
                tipo_comando='activate_actuator' if estado else 'deactivate_actuator',
                comando=f'SET_ACTUATOR:{actuador.pin_arduino}:{estado_str}:{actuador.tipo}',
                parametros={
                    'actuador_id': actuador.id,
                    'estado': estado,
                    'duracion_minutos': duracion
                },
                prioridad=2
            )

            # Registrar evento
            EventoActuador.objects.create(
                actuador=actuador,
                tipo_evento='encendido' if estado else 'apagado',
                descripcion=descripcion or f'Control manual por {request.user.get_full_name()}',
                usuario=request.user,
                duracion_minutos=duracion,
                automatico=False
            )

            # Actualizar estado en el modelo (se actualizará cuando Arduino responda)
            # actuador.estado_actual = estado
            # actuador.save()

            return Response({
                'message': f'Actuador {"encendido" if estado else "apagado"}',
                'comando_id': comando.id
            })

        except Exception as e:
            return Response(
                {'error': f'Error controlando actuador: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def eventos(self, request, pk=None):
        """Obtiene el historial de eventos del actuador"""
        actuador = self.get_object()

        dias = int(request.query_params.get('dias', 7))
        limite = int(request.query_params.get('limite', 50))

        fecha_desde = timezone.now() - timedelta(days=dias)

        eventos = EventoActuador.objects.filter(
            actuador=actuador,
            timestamp__gte=fecha_desde
        ).order_by('-timestamp')[:limite]

        serializer = EventoActuadorSerializer(eventos, many=True)
        return Response(serializer.data)


class ProgramaRiegoViewSet(viewsets.ModelViewSet):
    """ViewSet para gestión de programas de riego"""
    queryset = ProgramaRiego.objects.all()
    serializer_class = ProgramaRiegoSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = ProgramaRiego.objects.all()
        sector = self.request.query_params.get('sector')
        activo = self.request.query_params.get('activo')

        if sector:
            queryset = queryset.filter(sector_id=sector)
        if activo is not None:
            queryset = queryset.filter(activo=activo.lower() == 'true')

        return queryset.order_by('sector__nombre', 'hora_inicio')


class LecturaSensorViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet para lecturas de sensores (solo lectura)"""
    queryset = LecturaSensor.objects.all()
    serializer_class = LecturaSensorSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = LecturaSensor.objects.all()
        sensor = self.request.query_params.get('sensor')
        fecha_desde = self.request.query_params.get('fecha_desde')
        fecha_hasta = self.request.query_params.get('fecha_hasta')

        if sensor:
            queryset = queryset.filter(sensor_id=sensor)
        if fecha_desde:
            queryset = queryset.filter(timestamp__gte=fecha_desde)
        if fecha_hasta:
            queryset = queryset.filter(timestamp__lte=fecha_hasta)

        return queryset.order_by('-timestamp')


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_data(request):
    """Vista para datos del dashboard principal"""
    hoy = timezone.now().date()

    # Contadores básicos
    sectores_activos = Sector.objects.filter(activo=True).count()
    sensores_activos = Sensor.objects.filter(activo=True).count()
    actuadores_activos = Actuador.objects.filter(activo=True).count()

    # Actividad de hoy
    lecturas_hoy = LecturaSensor.objects.filter(timestamp__date=hoy).count()
    eventos_hoy = EventoActuador.objects.filter(timestamp__date=hoy).count()

    # Estado del Arduino
    estado_arduino = EstadoArduino.objects.first()
    arduino_conectado = estado_arduino.conectado if estado_arduino else False
    ultimo_ping = estado_arduino.ultimo_ping if estado_arduino else None

    # Alertas activas (sensores con valores fuera de rango)
    alertas_activas = 0
    for sensor in Sensor.objects.filter(activo=True):
        ultima_lectura = sensor.lecturas.first()
        if ultima_lectura:
            valor = ultima_lectura.valor_calibrado
            if valor < sensor.valor_minimo or valor > sensor.valor_maximo:
                alertas_activas += 1

    data = {
        'sectores_activos': sectores_activos,
        'sensores_activos': sensores_activos,
        'actuadores_activos': actuadores_activos,
        'lecturas_hoy': lecturas_hoy,
        'eventos_hoy': eventos_hoy,
        'arduino_conectado': arduino_conectado,
        'ultimo_ping': ultimo_ping,
        'alertas_activas': alertas_activas
    }

    serializer = DashboardDataSerializer(data)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def estadisticas_generales(request):
    """Vista para estadísticas generales del sistema"""
    dias = int(request.GET.get('dias', 7))
    fecha_desde = timezone.now() - timedelta(days=dias)

    # Estadísticas por sector
    sectores_stats = []
    for sector in Sector.objects.filter(activo=True):
        # Sensores del sector
        sensores = sector.sensores.filter(activo=True)

        # Promedios de lecturas
        lecturas_periodo = LecturaSensor.objects.filter(
            sensor__in=sensores,
            timestamp__gte=fecha_desde
        )

        humedad_promedio = lecturas_periodo.filter(
            sensor__tipo='humedad_suelo'
        ).aggregate(Avg('valor_calibrado'))['valor_calibrado__avg'] or 0

        temperatura_promedio = lecturas_periodo.filter(
            sensor__tipo='temperatura'
        ).aggregate(Avg('valor_calibrado'))['valor_calibrado__avg'] or 0

        # Eventos de riego
        eventos_riego = EventoActuador.objects.filter(
            actuador__sector=sector,
            actuador__tipo='valvula_riego',
            tipo_evento='encendido',
            timestamp__gte=fecha_desde
        )

        riegos_periodo = eventos_riego.count()
        tiempo_riego_total = sum([
            evento.duracion_minutos or 0 for evento in eventos_riego
        ])

        # Estado actual de válvula
        valvula = sector.actuadores.filter(tipo='valvula_riego').first()
        estado_valvula = valvula.estado_actual if valvula else False

        sectores_stats.append({
            'sector_id': sector.id,
            'sector_nombre': sector.nombre,
            'humedad_promedio': round(humedad_promedio, 1),
            'temperatura_promedio': round(temperatura_promedio, 1),
            'riegos_periodo': riegos_periodo,
            'tiempo_riego_total': tiempo_riego_total,
            'estado_valvula': estado_valvula
        })

    # Estadísticas de Arduino
    arduino_stats = {}
    estado_arduino = EstadoArduino.objects.first()
    if estado_arduino:
        arduino_stats = {
            'conectado': estado_arduino.conectado,
            'comandos_ejecutados': estado_arduino.comandos_ejecutados,
            'errores_comunicacion': estado_arduino.errores_comunicacion,
            'uptime_horas': estado_arduino.uptime_segundos / 3600 if estado_arduino.uptime_segundos else 0,
            'memoria_libre': estado_arduino.memoria_libre,
            'temperatura_cpu': estado_arduino.temperatura_cpu,
            'voltaje_alimentacion': estado_arduino.voltaje_alimentacion
        }

    return Response({
        'periodo_dias': dias,
        'sectores': sectores_stats,
        'arduino': arduino_stats,
        'resumen': {
            'total_sectores': len(sectores_stats),
            'total_riegos': sum([s['riegos_periodo'] for s in sectores_stats]),
            'tiempo_riego_total': sum([s['tiempo_riego_total'] for s in sectores_stats]),
            'humedad_promedio_general': sum([s['humedad_promedio'] for s in sectores_stats]) / len(sectores_stats) if sectores_stats else 0
        }
    })
