#!/usr/bin/env python
"""
Script para crear el superusuario con las credenciales especificadas
"""
import os
import sys
import django

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'riego_agricola.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import PerfilUsuario

def create_superuser():
    """Crea el superusuario Pablo con las credenciales especificadas"""
    
    # Credenciales especificadas por el usuario
    username = '<PERSON>'
    email = '<PERSON>@rebollotoro.com'
    password = 'ppp4525@'
    
    # Verificar si ya existe
    if User.objects.filter(username=username).exists():
        print(f"El usuario '{username}' ya existe.")
        return
    
    # Crear superusuario
    user = User.objects.create_superuser(
        username=username,
        email=email,
        password=password,
        first_name='<PERSON>',
        last_name='<PERSON><PERSON><PERSON>'
    )
    
    # Crear perfil de usuario
    perfil = PerfilUsuario.objects.create(
        user=user,
        telefono='+34 600 000 000',
        rol='admin',
        notificaciones_email=True,
        notificaciones_sms=False
    )
    
    print(f"✓ Superusuario creado exitosamente:")
    print(f"  Usuario: {username}")
    print(f"  Email: {email}")
    print(f"  Contraseña: {password}")
    print(f"  Rol: {perfil.get_rol_display()}")
    print(f"  ID: {user.id}")

if __name__ == '__main__':
    create_superuser()
