#!/usr/bin/env python
"""
Script para crear datos de prueba del sistema de riego
"""
import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone
import random

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'riego_agricola.settings')
django.setup()

from core.models import (
    Sector, Sensor, Actuador, LecturaSensor, 
    EventoActuador, ProgramaRiego, ConfiguracionSistema
)
from arduino_comm.models import ConfiguracionArduino, EstadoArduino
from django.contrib.auth.models import User

def create_test_data():
    """Crea datos de prueba para el sistema"""
    
    print("Creando datos de prueba...")
    
    # 1. Configuración del sistema
    configuraciones = [
        ('SISTEMA_NOMBRE', 'Sistema de Riego Agrícola', 'Nombre del sistema', 'string'),
        ('INTERVALO_LECTURA_SENSORES', '30', 'Intervalo de lectura de sensores en segundos', 'integer'),
        ('TIEMPO_RIEGO_DEFAULT', '15', 'Tiempo de riego por defecto en minutos', 'integer'),
        ('HUMEDAD_MINIMA_DEFAULT', '30', 'Humedad mínima por defecto (%)', 'integer'),
        ('NOTIFICACIONES_ACTIVAS', 'True', 'Activar notificaciones del sistema', 'boolean'),
        ('EMAIL_ALERTAS', '<EMAIL>', 'Email para alertas del sistema', 'string'),
    ]
    
    for clave, valor, descripcion, tipo_dato in configuraciones:
        ConfiguracionSistema.objects.get_or_create(
            clave=clave,
            defaults={
                'valor': valor,
                'descripcion': descripcion,
                'tipo_dato': tipo_dato
            }
        )
    
    # 2. Configuración Arduino
    config_arduino, created = ConfiguracionArduino.objects.get_or_create(
        activa=True,
        defaults={
            'puerto_serie': '/dev/ttyUSB0',
            'baudrate': 9600,
            'timeout_segundos': 5,
            'reintentos_conexion': 3,
            'intervalo_ping_segundos': 30
        }
    )
    
    # 3. Estado Arduino
    estado_arduino, created = EstadoArduino.objects.get_or_create(
        defaults={
            'puerto_serie': '/dev/ttyUSB0',
            'baudrate': 9600,
            'conectado': False,
            'comandos_ejecutados': 0,
            'errores_comunicacion': 0
        }
    )
    
    # 4. Sectores de prueba
    sectores_data = [
        {
            'nombre': 'Sector A - Tomates',
            'descripcion': 'Sector principal de cultivo de tomates',
            'area_m2': 500,
            'tipo_cultivo': 'tomate',
            'pin_valvula': 2,
            'tiempo_riego_minutos': 20,
            'humedad_minima': 35
        },
        {
            'nombre': 'Sector B - Lechugas',
            'descripcion': 'Sector de lechugas hidropónicas',
            'area_m2': 300,
            'tipo_cultivo': 'lechuga',
            'pin_valvula': 3,
            'tiempo_riego_minutos': 15,
            'humedad_minima': 40
        },
        {
            'nombre': 'Sector C - Pimientos',
            'descripcion': 'Sector de pimientos rojos',
            'area_m2': 400,
            'tipo_cultivo': 'pimiento',
            'pin_valvula': 4,
            'tiempo_riego_minutos': 18,
            'humedad_minima': 30
        }
    ]
    
    sectores = []
    for sector_data in sectores_data:
        sector, created = Sector.objects.get_or_create(
            nombre=sector_data['nombre'],
            defaults=sector_data
        )
        sectores.append(sector)
        if created:
            print(f"✓ Sector creado: {sector.nombre}")
    
    # 5. Sensores para cada sector
    tipos_sensores = [
        ('humedad_suelo', 'Humedad del Suelo', '%', 0, 100),
        ('temperatura', 'Temperatura Ambiente', '°C', -10, 50),
        ('ph_suelo', 'pH del Suelo', 'pH', 0, 14),
        ('conductividad', 'Conductividad', 'µS/cm', 0, 5000)
    ]
    
    pin_sensor = 0
    for sector in sectores:
        for tipo, nombre_base, unidad, min_val, max_val in tipos_sensores:
            sensor, created = Sensor.objects.get_or_create(
                nombre=f"{nombre_base} - {sector.nombre}",
                sector=sector,
                defaults={
                    'tipo': tipo,
                    'pin_arduino': pin_sensor,
                    'activo': True,
                    'valor_minimo': min_val,
                    'valor_maximo': max_val,
                    'unidad_medida': unidad,
                    'calibracion_offset': 0,
                    'calibracion_factor': 1.0
                }
            )
            pin_sensor += 1
            if created:
                print(f"✓ Sensor creado: {sensor.nombre}")
    
    # 6. Actuadores para cada sector
    tipos_actuadores = [
        ('valvula_riego', 'Válvula de Riego', 100, 480),
        ('bomba_fertilizante', 'Bomba Fertilizante', 50, 60),
        ('ventilador', 'Ventilador', 30, 120)
    ]
    
    pin_actuador = 2
    for sector in sectores:
        for tipo, nombre_base, potencia, tiempo_max in tipos_actuadores:
            actuador, created = Actuador.objects.get_or_create(
                nombre=f"{nombre_base} - {sector.nombre}",
                sector=sector,
                defaults={
                    'tipo': tipo,
                    'pin_arduino': pin_actuador,
                    'activo': True,
                    'estado_actual': False,
                    'potencia_watts': potencia,
                    'tiempo_activacion_max': tiempo_max
                }
            )
            pin_actuador += 1
            if created:
                print(f"✓ Actuador creado: {actuador.nombre}")
    
    # 7. Programas de riego
    horas_riego = ['06:00', '12:00', '18:00']
    for i, sector in enumerate(sectores):
        programa, created = ProgramaRiego.objects.get_or_create(
            nombre=f"Riego Automático - {sector.nombre}",
            sector=sector,
            defaults={
                'activo': True,
                'hora_inicio': horas_riego[i % len(horas_riego)],
                'duracion_minutos': sector.tiempo_riego_minutos,
                'dias_semana': [1, 2, 3, 4, 5, 6, 7],  # Todos los días
                'usar_sensor_humedad': True,
                'humedad_minima_activacion': sector.humedad_minima
            }
        )
        if created:
            print(f"✓ Programa de riego creado: {programa.nombre}")
    
    # 8. Lecturas de sensores de prueba (últimos 7 días)
    print("Generando lecturas de sensores de prueba...")
    sensores = Sensor.objects.filter(activo=True)
    
    for sensor in sensores:
        # Generar lecturas para los últimos 7 días
        for dias_atras in range(7):
            fecha_base = timezone.now() - timedelta(days=dias_atras)
            
            # Generar varias lecturas por día
            for hora in range(0, 24, 2):  # Cada 2 horas
                timestamp = fecha_base.replace(hour=hora, minute=0, second=0, microsecond=0)
                
                # Generar valor realista según el tipo de sensor
                if sensor.tipo == 'humedad_suelo':
                    valor = random.uniform(25, 80)
                elif sensor.tipo == 'temperatura':
                    valor = random.uniform(18, 35)
                elif sensor.tipo == 'ph_suelo':
                    valor = random.uniform(6.0, 7.5)
                elif sensor.tipo == 'conductividad':
                    valor = random.uniform(800, 2000)
                else:
                    valor = random.uniform(sensor.valor_minimo, sensor.valor_maximo)
                
                LecturaSensor.objects.get_or_create(
                    sensor=sensor,
                    timestamp=timestamp,
                    defaults={'valor': valor}
                )
    
    # 9. Eventos de actuadores de prueba
    print("Generando eventos de actuadores de prueba...")
    usuario_admin = User.objects.filter(is_superuser=True).first()
    actuadores = Actuador.objects.filter(activo=True, tipo='valvula_riego')
    
    for actuador in actuadores:
        # Generar algunos eventos de riego
        for dias_atras in range(7):
            if random.random() < 0.7:  # 70% probabilidad de riego por día
                timestamp = timezone.now() - timedelta(
                    days=dias_atras,
                    hours=random.randint(6, 18),
                    minutes=random.randint(0, 59)
                )
                
                EventoActuador.objects.get_or_create(
                    actuador=actuador,
                    timestamp=timestamp,
                    defaults={
                        'tipo_evento': 'encendido',
                        'descripcion': 'Riego automático programado',
                        'usuario': usuario_admin,
                        'duracion_minutos': random.randint(10, 25),
                        'automatico': True
                    }
                )
    
    print("\n✅ Datos de prueba creados exitosamente!")
    print(f"📊 Sectores: {Sector.objects.count()}")
    print(f"🌡️  Sensores: {Sensor.objects.count()}")
    print(f"⚙️  Actuadores: {Actuador.objects.count()}")
    print(f"📈 Lecturas: {LecturaSensor.objects.count()}")
    print(f"🔄 Eventos: {EventoActuador.objects.count()}")
    print(f"⏰ Programas: {ProgramaRiego.objects.count()}")

if __name__ == '__main__':
    create_test_data()
