import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser


class DashboardConsumer(AsyncWebsocketConsumer):
    """Consumer para actualizaciones en tiempo real del dashboard"""
    
    async def connect(self):
        # Verificar autenticación
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
        
        self.group_name = 'dashboard_updates'
        
        # Unirse al grupo
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Enviar datos iniciales
        await self.send_dashboard_data()
    
    async def disconnect(self, close_code):
        # Salir del grupo
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """Maneja mensajes del cliente"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'request_update':
                await self.send_dashboard_data()
            elif message_type == 'ping':
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': data.get('timestamp')
                }))
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON'
            }))
    
    async def send_dashboard_data(self):
        """Envía datos actualizados del dashboard"""
        data = await self.get_dashboard_data()
        await self.send(text_data=json.dumps({
            'type': 'dashboard_update',
            'data': data
        }))
    
    async def dashboard_update(self, event):
        """Maneja actualizaciones del dashboard desde el grupo"""
        await self.send(text_data=json.dumps({
            'type': 'dashboard_update',
            'data': event['data']
        }))
    
    async def sensor_reading(self, event):
        """Maneja nuevas lecturas de sensores"""
        await self.send(text_data=json.dumps({
            'type': 'sensor_reading',
            'data': event['data']
        }))
    
    async def actuator_event(self, event):
        """Maneja eventos de actuadores"""
        await self.send(text_data=json.dumps({
            'type': 'actuator_event',
            'data': event['data']
        }))
    
    @database_sync_to_async
    def get_dashboard_data(self):
        """Obtiene datos del dashboard desde la base de datos"""
        from core.models import Sector, Sensor, Actuador, LecturaSensor, EventoActuador
        from arduino_comm.models import EstadoArduino
        from django.utils import timezone
        
        hoy = timezone.now().date()
        
        # Estado del Arduino
        estado_arduino = EstadoArduino.objects.first()
        arduino_conectado = estado_arduino.conectado if estado_arduino else False
        
        # Contadores básicos
        data = {
            'sectores_activos': Sector.objects.filter(activo=True).count(),
            'sensores_activos': Sensor.objects.filter(activo=True).count(),
            'actuadores_activos': Actuador.objects.filter(activo=True).count(),
            'lecturas_hoy': LecturaSensor.objects.filter(timestamp__date=hoy).count(),
            'eventos_hoy': EventoActuador.objects.filter(timestamp__date=hoy).count(),
            'arduino_conectado': arduino_conectado,
            'timestamp': timezone.now().isoformat()
        }
        
        # Últimas lecturas de sensores críticos
        sensores_criticos = []
        for sensor in Sensor.objects.filter(activo=True, tipo__in=['humedad_suelo', 'temperatura']):
            ultima_lectura = sensor.lecturas.first()
            if ultima_lectura:
                sensores_criticos.append({
                    'id': sensor.id,
                    'nombre': sensor.nombre,
                    'tipo': sensor.tipo,
                    'valor': ultima_lectura.valor_calibrado,
                    'unidad': sensor.unidad_medida,
                    'timestamp': ultima_lectura.timestamp.isoformat(),
                    'sector': sensor.sector.nombre if sensor.sector else None
                })
        
        data['sensores_criticos'] = sensores_criticos
        
        return data


class SectorConsumer(AsyncWebsocketConsumer):
    """Consumer para actualizaciones específicas de un sector"""
    
    async def connect(self):
        # Verificar autenticación
        if self.scope["user"] == AnonymousUser():
            await self.close()
            return
        
        self.sector_id = self.scope['url_route']['kwargs']['sector_id']
        self.group_name = f'sector_{self.sector_id}'
        
        # Verificar que el sector existe
        sector_exists = await self.check_sector_exists(self.sector_id)
        if not sector_exists:
            await self.close()
            return
        
        # Unirse al grupo
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Enviar datos iniciales del sector
        await self.send_sector_data()
    
    async def disconnect(self, close_code):
        # Salir del grupo
        await self.channel_layer.group_discard(
            self.group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        """Maneja mensajes del cliente"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'request_update':
                await self.send_sector_data()
            elif message_type == 'control_actuator':
                # Manejar control de actuadores
                await self.handle_actuator_control(data)
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON'
            }))
    
    async def send_sector_data(self):
        """Envía datos actualizados del sector"""
        data = await self.get_sector_data()
        await self.send(text_data=json.dumps({
            'type': 'sector_update',
            'data': data
        }))
    
    async def sector_update(self, event):
        """Maneja actualizaciones del sector desde el grupo"""
        await self.send(text_data=json.dumps({
            'type': 'sector_update',
            'data': event['data']
        }))
    
    async def handle_actuator_control(self, data):
        """Maneja comandos de control de actuadores"""
        # Aquí se implementaría la lógica para controlar actuadores
        # Por ahora solo enviamos confirmación
        await self.send(text_data=json.dumps({
            'type': 'actuator_control_response',
            'success': True,
            'message': 'Comando enviado'
        }))
    
    @database_sync_to_async
    def check_sector_exists(self, sector_id):
        """Verifica si el sector existe"""
        from core.models import Sector
        return Sector.objects.filter(id=sector_id).exists()
    
    @database_sync_to_async
    def get_sector_data(self):
        """Obtiene datos específicos del sector"""
        from core.models import Sector, LecturaSensor, EventoActuador
        from django.utils import timezone
        
        try:
            sector = Sector.objects.get(id=self.sector_id)
            
            # Sensores del sector
            sensores_data = []
            for sensor in sector.sensores.filter(activo=True):
                ultima_lectura = sensor.lecturas.first()
                sensores_data.append({
                    'id': sensor.id,
                    'nombre': sensor.nombre,
                    'tipo': sensor.tipo,
                    'valor': ultima_lectura.valor_calibrado if ultima_lectura else None,
                    'unidad': sensor.unidad_medida,
                    'timestamp': ultima_lectura.timestamp.isoformat() if ultima_lectura else None
                })
            
            # Actuadores del sector
            actuadores_data = []
            for actuador in sector.actuadores.filter(activo=True):
                ultimo_evento = actuador.eventos.first()
                actuadores_data.append({
                    'id': actuador.id,
                    'nombre': actuador.nombre,
                    'tipo': actuador.tipo,
                    'estado_actual': actuador.estado_actual,
                    'ultimo_evento': {
                        'tipo': ultimo_evento.tipo_evento,
                        'timestamp': ultimo_evento.timestamp.isoformat()
                    } if ultimo_evento else None
                })
            
            return {
                'sector': {
                    'id': sector.id,
                    'nombre': sector.nombre,
                    'tipo_cultivo': sector.tipo_cultivo,
                    'activo': sector.activo
                },
                'sensores': sensores_data,
                'actuadores': actuadores_data,
                'timestamp': timezone.now().isoformat()
            }
            
        except Sector.DoesNotExist:
            return {'error': 'Sector no encontrado'}
