from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.utils import timezone
from django.contrib import messages
from django.db.models import Avg, Count
from datetime import timedelta
import json

from core.models import (
    Sector, Sensor, Actuador, LecturaSensor,
    EventoActuador, ProgramaRiego
)
from arduino_comm.models import EstadoArduino, ComandoArduino
from users.models import NotificacionUsuario


@login_required
def home(request):
    """Vista principal del dashboard"""
    # Datos básicos para el dashboard
    hoy = timezone.now().date()

    context = {
        'sectores_activos': Sector.objects.filter(activo=True).count(),
        'sensores_activos': Sensor.objects.filter(activo=True).count(),
        'actuadores_activos': Actuador.objects.filter(activo=True).count(),
        'lecturas_hoy': LecturaSensor.objects.filter(timestamp__date=hoy).count(),
        'eventos_hoy': EventoActuador.objects.filter(timestamp__date=hoy).count(),
    }

    # Estado del Arduino
    estado_arduino = EstadoArduino.objects.first()
    context['arduino_conectado'] = estado_arduino.conectado if estado_arduino else False

    # Notificaciones no leídas
    context['notificaciones_no_leidas'] = NotificacionUsuario.objects.filter(
        usuario=request.user,
        leida=False
    ).count()

    return render(request, 'dashboard/home.html', context)


@login_required
def sectores(request):
    """Vista de gestión de sectores"""
    sectores_list = Sector.objects.all().order_by('nombre')

    # Agregar información adicional a cada sector
    for sector in sectores_list:
        # Última lectura de humedad
        sensor_humedad = sector.sensores.filter(
            tipo='humedad_suelo',
            activo=True
        ).first()

        if sensor_humedad:
            ultima_lectura = sensor_humedad.lecturas.first()
            sector.humedad_actual = ultima_lectura.valor_calibrado if ultima_lectura else None
        else:
            sector.humedad_actual = None

        # Estado de la válvula
        valvula = sector.actuadores.filter(tipo='valvula_riego').first()
        sector.valvula_activa = valvula.estado_actual if valvula else False

        # Contadores
        sector.sensores_count = sector.sensores.filter(activo=True).count()
        sector.actuadores_count = sector.actuadores.filter(activo=True).count()

    return render(request, 'dashboard/sectores.html', {
        'sectores': sectores_list
    })


@login_required
def sensores(request):
    """Vista de gestión de sensores"""
    sensores_list = Sensor.objects.select_related('sector').filter(
        activo=True
    ).order_by('sector__nombre', 'nombre')

    # Agregar última lectura a cada sensor
    for sensor in sensores_list:
        ultima_lectura = sensor.lecturas.first()
        sensor.ultima_lectura = ultima_lectura

    return render(request, 'dashboard/sensores.html', {
        'sensores': sensores_list
    })


@login_required
def actuadores(request):
    """Vista de gestión de actuadores"""
    actuadores_list = Actuador.objects.select_related('sector').filter(
        activo=True
    ).order_by('sector__nombre', 'nombre')

    # Agregar último evento a cada actuador
    for actuador in actuadores_list:
        ultimo_evento = actuador.eventos.first()
        actuador.ultimo_evento = ultimo_evento

    return render(request, 'dashboard/actuadores.html', {
        'actuadores': actuadores_list
    })


@login_required
def programas(request):
    """Vista de gestión de programas de riego"""
    programas_list = ProgramaRiego.objects.select_related('sector').order_by(
        'sector__nombre', 'hora_inicio'
    )

    return render(request, 'dashboard/programas.html', {
        'programas': programas_list
    })


@login_required
def sector_detalle(request, sector_id):
    """Vista detallada de un sector específico"""
    sector = get_object_or_404(Sector, id=sector_id)

    # Sensores del sector
    sensores = sector.sensores.filter(activo=True)

    # Actuadores del sector
    actuadores = sector.actuadores.filter(activo=True)

    # Programas de riego
    programas = sector.programas_riego.all()

    # Últimas lecturas (últimas 24 horas)
    hace_24h = timezone.now() - timedelta(hours=24)
    lecturas_recientes = LecturaSensor.objects.filter(
        sensor__in=sensores,
        timestamp__gte=hace_24h
    ).order_by('-timestamp')[:50]

    # Eventos recientes
    eventos_recientes = EventoActuador.objects.filter(
        actuador__in=actuadores,
        timestamp__gte=hace_24h
    ).order_by('-timestamp')[:20]

    context = {
        'sector': sector,
        'sensores': sensores,
        'actuadores': actuadores,
        'programas': programas,
        'lecturas_recientes': lecturas_recientes,
        'eventos_recientes': eventos_recientes,
    }

    return render(request, 'dashboard/sector_detalle.html', context)
