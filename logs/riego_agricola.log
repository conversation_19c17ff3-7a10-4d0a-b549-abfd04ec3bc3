INFO 2025-09-20 16:33:41,092 autoreload 5182 ********** Watching for file changes with StatReloader
INFO 2025-09-20 16:34:01,359 basehttp 5182 ********** "GET / HTTP/1.1" 302 0
INFO 2025-09-20 16:34:01,360 basehttp 5182 ********** "GET /dashboard/ HTTP/1.1" 302 0
WARNING 2025-09-20 16:34:01,376 log 5182 ********** Not Found: /accounts/login/
WARNING 2025-09-20 16:34:01,376 basehttp 5182 ********** "GET /accounts/login/?next=/dashboard/ HTTP/1.1" 404 2930
WARNING 2025-09-20 16:34:01,450 log 5182 ********** Not Found: /favicon.ico
WARNING 2025-09-20 16:34:01,451 basehttp 5182 ********** "GET /favicon.ico HTTP/1.1" 404 2901
INFO 2025-09-20 16:34:35,093 autoreload 5182 ********** /Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/riego_agricola/settings.py changed, reloading.
INFO 2025-09-20 16:34:35,552 autoreload 5287 ********** Watching for file changes with StatReloader
INFO 2025-09-20 16:34:47,928 autoreload 5385 ********** Watching for file changes with StatReloader
INFO 2025-09-20 16:35:02,181 basehttp 5385 ********** "GET / HTTP/1.1" 302 0
INFO 2025-09-20 16:35:02,182 basehttp 5385 ********** "GET /dashboard/ HTTP/1.1" 302 0
INFO 2025-09-20 16:35:02,189 basehttp 5385 ********** "GET /users/login/?next=/dashboard/ HTTP/1.1" 200 8107
INFO 2025-09-20 16:35:13,127 basehttp 5385 ********** "POST /users/login/?next=/dashboard/ HTTP/1.1" 302 0
INFO 2025-09-20 16:35:13,149 basehttp 5385 ********** "GET /dashboard/ HTTP/1.1" 200 21599
INFO 2025-09-20 16:35:13,582 basehttp 5385 ********** "GET /core/api/actuadores/?activo=true HTTP/1.1" 200 3493
WARNING 2025-09-20 16:35:13,584 log 5385 6234763264 Not Found: /ws/dashboard/
WARNING 2025-09-20 16:35:13,585 basehttp 5385 6234763264 "GET /ws/dashboard/ HTTP/1.1" 404 2907
WARNING 2025-09-20 16:35:18,628 log 5385 6234763264 Not Found: /ws/dashboard/
WARNING 2025-09-20 16:35:18,628 basehttp 5385 6234763264 "GET /ws/dashboard/ HTTP/1.1" 404 2907
INFO 2025-09-20 16:35:22,276 basehttp 5385 ********** "GET /dashboard/sectores/ HTTP/1.1" 200 29597
INFO 2025-09-20 16:35:33,935 basehttp 5385 ********** "POST /core/api/sectores/1/activar_riego/ HTTP/1.1" 200 59
INFO 2025-09-20 16:35:39,247 basehttp 5385 ********** "GET /dashboard/sectores/ HTTP/1.1" 200 29597
INFO 2025-09-20 16:46:32,949 basehttp 5385 ********** "GET /dashboard/ HTTP/1.1" 200 21280
WARNING 2025-09-20 16:46:33,144 log 5385 6234763264 Not Found: /ws/dashboard/
WARNING 2025-09-20 16:46:33,145 basehttp 5385 6234763264 "GET /ws/dashboard/ HTTP/1.1" 404 2907
INFO 2025-09-20 16:46:33,153 basehttp 5385 ********** "GET /core/api/actuadores/?activo=true HTTP/1.1" 200 3494
WARNING 2025-09-20 16:46:38,225 log 5385 6234763264 Not Found: /ws/dashboard/
WARNING 2025-09-20 16:46:38,226 basehttp 5385 6234763264 "GET /ws/dashboard/ HTTP/1.1" 404 2907
INFO 2025-09-20 16:46:38,226 basehttp 5385 6234763264 - Broken pipe from ('127.0.0.1', 51603)
WARNING 2025-09-20 16:46:43,281 log 5385 6234763264 Not Found: /ws/dashboard/
WARNING 2025-09-20 16:46:43,282 basehttp 5385 6234763264 "GET /ws/dashboard/ HTTP/1.1" 404 2907
WARNING 2025-09-20 16:46:48,311 log 5385 6234763264 Not Found: /ws/dashboard/
WARNING 2025-09-20 16:46:48,312 basehttp 5385 6234763264 "GET /ws/dashboard/ HTTP/1.1" 404 2907
INFO 2025-09-20 16:46:51,407 basehttp 5385 ********** "GET /dashboard/sectores/ HTTP/1.1" 200 29597
ERROR 2025-09-20 16:46:52,803 log 5385 ********** Internal Server Error: /dashboard/sensores/
Traceback (most recent call last):
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/dashboard/views.py", line 89, in sensores
    return render(request, 'dashboard/sensores.html', {
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/sensores.html
ERROR 2025-09-20 16:46:52,806 basehttp 5385 ********** "GET /dashboard/sensores/ HTTP/1.1" 500 94591
ERROR 2025-09-20 16:46:56,136 log 5385 ********** Internal Server Error: /dashboard/actuadores/
Traceback (most recent call last):
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/dashboard/views.py", line 106, in actuadores
    return render(request, 'dashboard/actuadores.html', {
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/Users/<USER>/Library/CloudStorage/<EMAIL>/Mi unidad/Pablo/Programa v1/venv/lib/python3.9/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/actuadores.html
ERROR 2025-09-20 16:46:56,137 basehttp 5385 ********** "GET /dashboard/actuadores/ HTTP/1.1" 500 94217
INFO 2025-09-20 16:46:59,038 basehttp 5385 ********** "GET /users/perfil/ HTTP/1.1" 200 16623
INFO 2025-09-20 16:47:15,056 basehttp 5385 ********** "GET /users/logout/ HTTP/1.1" 302 0
INFO 2025-09-20 16:47:15,058 basehttp 5385 ********** "GET /users/login/ HTTP/1.1" 200 8514
INFO 2025-09-20 16:47:31,413 basehttp 5385 ********** "POST /users/login/ HTTP/1.1" 302 0
INFO 2025-09-20 16:47:31,423 basehttp 5385 ********** "GET /dashboard/ HTTP/1.1" 200 21599
WARNING 2025-09-20 16:47:31,484 log 5385 6234763264 Not Found: /ws/dashboard/
WARNING 2025-09-20 16:47:31,484 basehttp 5385 6234763264 "GET /ws/dashboard/ HTTP/1.1" 404 2907
INFO 2025-09-20 16:47:31,490 basehttp 5385 ********** "GET /core/api/actuadores/?activo=true HTTP/1.1" 200 3494
WARNING 2025-09-20 16:47:36,538 log 5385 6217936896 Not Found: /ws/dashboard/
WARNING 2025-09-20 16:47:36,538 basehttp 5385 6217936896 "GET /ws/dashboard/ HTTP/1.1" 404 2907
INFO 2025-09-20 16:47:36,539 basehttp 5385 6217936896 - Broken pipe from ('127.0.0.1', 51618)
INFO 2025-09-20 16:47:37,308 basehttp 5385 ********** "GET /dashboard/sectores/ HTTP/1.1" 200 29597
