"""
URL configuration for riego_agricola project.

Sistema de Riego Agrícola Automatizado
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import redirect

def redirect_to_dashboard(request):
    """Redirige la raíz al dashboard"""
    return redirect('dashboard:home')

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # Redirección de raíz
    path('', redirect_to_dashboard),

    # Apps principales
    path('users/', include('users.urls')),
    path('dashboard/', include('dashboard.urls')),
    path('core/', include('core.urls')),

    # API de Django REST Framework
    path('api-auth/', include('rest_framework.urls')),
]

# Servir archivos media en desarrollo
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
