{% extends 'base.html' %}
{% load static %}

{% block title %}Actuadores - Sistema de Riego{% endblock %}

{% block extra_css %}
<style>
    .actuator-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        border-left: 5px solid #007bff;
    }
    
    .actuator-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .actuator-card.active {
        border-left-color: #28a745;
        background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
    }
    
    .actuator-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: white;
        margin-bottom: 1rem;
    }
    
    .icon-valve {
        background: linear-gradient(45deg, #007bff, #0056b3);
    }
    
    .icon-pump {
        background: linear-gradient(45deg, #28a745, #1e7e34);
    }
    
    .icon-fan {
        background: linear-gradient(45deg, #6c757d, #495057);
    }
    
    .status-indicator {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    
    .status-on {
        background-color: #28a745;
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        animation: pulse 2s infinite;
    }
    
    .status-off {
        background-color: #6c757d;
    }
    
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
        100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
    }
    
    .control-button {
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        min-width: 120px;
    }
    
    .btn-turn-on {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }
    
    .btn-turn-on:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        color: white;
    }
    
    .btn-turn-off {
        background: linear-gradient(45deg, #dc3545, #c82333);
        color: white;
    }
    
    .btn-turn-off:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        color: white;
    }
    
    .power-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 0.75rem;
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">
                    <i class="fas fa-cogs text-primary me-2"></i>
                    Control de Actuadores
                </h1>
                <div>
                    <button class="btn btn-warning me-2" onclick="emergencyStop()">
                        <i class="fas fa-stop me-2"></i>Parada de Emergencia
                    </button>
                    <button class="btn btn-primary" onclick="refreshActuators()">
                        <i class="fas fa-sync-alt me-2"></i>Actualizar Estado
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchActuator" placeholder="Buscar actuador...">
            </div>
        </div>
        <div class="col-md-4">
            <select class="form-select" id="filterSector">
                <option value="">Todos los sectores</option>
                <option value="1">Sector A - Tomates</option>
                <option value="2">Sector B - Lechugas</option>
                <option value="3">Sector C - Pimientos</option>
            </select>
        </div>
        <div class="col-md-4">
            <select class="form-select" id="filterType">
                <option value="">Todos los tipos</option>
                <option value="valvula_riego">Válvulas de Riego</option>
                <option value="bomba_fertilizante">Bombas Fertilizante</option>
                <option value="ventilador">Ventiladores</option>
            </select>
        </div>
    </div>

    <!-- Lista de actuadores -->
    <div class="row" id="actuadores-container">
        {% for actuador in actuadores %}
        <div class="col-lg-4 col-md-6 mb-4 actuator-item" 
             data-name="{{ actuador.nombre|lower }}" 
             data-sector="{{ actuador.sector.id }}"
             data-type="{{ actuador.tipo }}">
            <div class="actuator-card {% if actuador.estado_actual %}active{% endif %}">
                <!-- Icono del actuador -->
                <div class="actuator-icon 
                    {% if actuador.tipo == 'valvula_riego' %}icon-valve
                    {% elif actuador.tipo == 'bomba_fertilizante' %}icon-pump
                    {% else %}icon-fan{% endif %}">
                    <i class="fas 
                        {% if actuador.tipo == 'valvula_riego' %}fa-faucet
                        {% elif actuador.tipo == 'bomba_fertilizante' %}fa-pump-soap
                        {% else %}fa-fan{% endif %}"></i>
                </div>

                <!-- Información del actuador -->
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5 class="mb-1">{{ actuador.nombre }}</h5>
                        <p class="text-muted mb-0">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ actuador.sector.nombre }}
                        </p>
                    </div>
                    <div class="text-end">
                        <div class="d-flex align-items-center mb-1">
                            <span class="status-indicator {% if actuador.estado_actual %}status-on{% else %}status-off{% endif %}"></span>
                            <strong class="{% if actuador.estado_actual %}text-success{% else %}text-muted{% endif %}">
                                {% if actuador.estado_actual %}ENCENDIDO{% else %}APAGADO{% endif %}
                            </strong>
                        </div>
                        <small class="text-muted">Pin {{ actuador.pin_arduino }}</small>
                    </div>
                </div>

                <!-- Último evento -->
                {% if actuador.ultimo_evento %}
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        Último evento: {{ actuador.ultimo_evento.timestamp|date:"d/m H:i" }}
                        {% if actuador.ultimo_evento.duracion_minutos %}
                        ({{ actuador.ultimo_evento.duracion_minutos }} min)
                        {% endif %}
                    </small>
                </div>
                {% endif %}

                <!-- Información de potencia -->
                <div class="power-info">
                    <div class="row text-center">
                        <div class="col-6">
                            <strong class="text-primary">{{ actuador.potencia_watts }}W</strong>
                            <br><small class="text-muted">Potencia</small>
                        </div>
                        <div class="col-6">
                            <strong class="text-info">{{ actuador.tiempo_activacion_max }}min</strong>
                            <br><small class="text-muted">Tiempo Máx.</small>
                        </div>
                    </div>
                </div>

                <!-- Controles -->
                <div class="text-center mt-4">
                    {% if actuador.estado_actual %}
                        <button class="control-button btn-turn-off" 
                                onclick="controlActuator({{ actuador.id }}, false, '{{ actuador.nombre }}')">
                            <i class="fas fa-stop me-2"></i>APAGAR
                        </button>
                    {% else %}
                        <button class="control-button btn-turn-on" 
                                onclick="controlActuator({{ actuador.id }}, true, '{{ actuador.nombre }}')">
                            <i class="fas fa-play me-2"></i>ENCENDER
                        </button>
                    {% endif %}
                </div>

                <!-- Programación rápida (solo para válvulas) -->
                {% if actuador.tipo == 'valvula_riego' %}
                <div class="mt-3 pt-3 border-top">
                    <small class="text-muted d-block mb-2">Riego rápido:</small>
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-outline-success btn-sm" 
                                onclick="quickIrrigation({{ actuador.id }}, 5)">5min</button>
                        <button class="btn btn-outline-success btn-sm" 
                                onclick="quickIrrigation({{ actuador.id }}, 10)">10min</button>
                        <button class="btn btn-outline-success btn-sm" 
                                onclick="quickIrrigation({{ actuador.id }}, 15)">15min</button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No hay actuadores configurados</h5>
                <p class="text-muted">Los actuadores aparecerán aquí cuando se configuren</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Filtros
    document.getElementById('searchActuator').addEventListener('input', filterActuators);
    document.getElementById('filterSector').addEventListener('change', filterActuators);
    document.getElementById('filterType').addEventListener('change', filterActuators);
    
    function filterActuators() {
        const search = document.getElementById('searchActuator').value.toLowerCase();
        const sector = document.getElementById('filterSector').value;
        const type = document.getElementById('filterType').value;
        
        const actuators = document.querySelectorAll('.actuator-item');
        
        actuators.forEach(actuator => {
            const name = actuator.dataset.name;
            const actuatorSector = actuator.dataset.sector;
            const actuatorType = actuator.dataset.type;
            
            let show = true;
            
            if (search && !name.includes(search)) show = false;
            if (sector && actuatorSector !== sector) show = false;
            if (type && actuatorType !== type) show = false;
            
            actuator.style.display = show ? 'block' : 'none';
        });
    }
    
    // Controlar actuador
    function controlActuator(actuatorId, newState, actuatorName) {
        const action = newState ? 'encender' : 'apagar';
        
        if (confirm(`¿${action.charAt(0).toUpperCase() + action.slice(1)} ${actuatorName}?`)) {
            fetch(`/core/api/actuadores/${actuatorId}/controlar/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    estado: newState,
                    descripcion: `Control manual desde dashboard - ${action}`
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    showNotification('success', data.message);
                    setTimeout(() => location.reload(), 1500);
                } else if (data.error) {
                    showNotification('error', data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('error', 'Error controlando actuador');
            });
        }
    }
    
    // Riego rápido
    function quickIrrigation(actuatorId, minutes) {
        if (confirm(`¿Activar riego por ${minutes} minutos?`)) {
            fetch(`/core/api/actuadores/${actuatorId}/controlar/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    estado: true,
                    duracion_minutos: minutes,
                    descripcion: `Riego rápido ${minutes} minutos desde dashboard`
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    showNotification('success', `Riego activado por ${minutes} minutos`);
                    setTimeout(() => location.reload(), 1500);
                } else if (data.error) {
                    showNotification('error', data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('error', 'Error activando riego');
            });
        }
    }
    
    // Parada de emergencia
    function emergencyStop() {
        if (confirm('¿PARAR TODOS LOS ACTUADORES? Esta acción detendrá inmediatamente todos los equipos.')) {
            showNotification('warning', 'Función de parada de emergencia en desarrollo');
        }
    }
    
    // Actualizar actuadores
    function refreshActuators() {
        location.reload();
    }
    
    // Utilidades
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    function showNotification(type, message) {
        alert(message);
    }
</script>
{% endblock %}
