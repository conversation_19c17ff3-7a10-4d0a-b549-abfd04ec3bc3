{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard - Sistema de Riego{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 1.5rem;
        color: white;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .metric-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    
    .status-online {
        background-color: #28a745;
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
    
    .status-offline {
        background-color: #dc3545;
        box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .sensor-card {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #007bff;
    }
    
    .actuator-control {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .btn-actuator {
        border-radius: 25px;
        padding: 0.5rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-actuator.active {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .btn-actuator.inactive {
        background: linear-gradient(45deg, #6c757d, #495057);
        border: none;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header con estado del sistema -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">Dashboard de Riego</h1>
                <div class="d-flex align-items-center">
                    <span class="status-indicator {% if arduino_conectado %}status-online{% else %}status-offline{% endif %}"></span>
                    <span class="me-3">Arduino {% if arduino_conectado %}Conectado{% else %}Desconectado{% endif %}</span>
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i> Actualizar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Métricas principales -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="metric-card">
                <div class="metric-value" id="sectores-count">{{ sectores_activos }}</div>
                <div class="metric-label">Sectores Activos</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="metric-value" id="sensores-count">{{ sensores_activos }}</div>
                <div class="metric-label">Sensores Activos</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="metric-value" id="actuadores-count">{{ actuadores_activos }}</div>
                <div class="metric-label">Actuadores Activos</div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="metric-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="metric-value" id="lecturas-hoy">{{ lecturas_hoy }}</div>
                <div class="metric-label">Lecturas Hoy</div>
            </div>
        </div>
    </div>

    <!-- Gráficos y controles -->
    <div class="row">
        <!-- Gráfico de sensores -->
        <div class="col-lg-8 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    Lecturas de Sensores en Tiempo Real
                </h5>
                <canvas id="sensorsChart" height="300"></canvas>
            </div>
        </div>

        <!-- Panel de control de actuadores -->
        <div class="col-lg-4 mb-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-cogs text-success me-2"></i>
                    Control de Actuadores
                </h5>
                <div id="actuators-panel">
                    <!-- Los actuadores se cargarán dinámicamente -->
                </div>
            </div>
        </div>
    </div>

    <!-- Sensores críticos -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-thermometer-half text-warning me-2"></i>
                    Estado de Sensores Críticos
                </h5>
                <div class="row" id="critical-sensors">
                    <!-- Los sensores se cargarán dinámicamente -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
    // Variables globales
    let sensorsChart;
    let websocket;
    
    // Inicializar dashboard
    document.addEventListener('DOMContentLoaded', function() {
        initializeChart();
        connectWebSocket();
        loadActuators();
        loadCriticalSensors();
    });
    
    // Inicializar gráfico de sensores
    function initializeChart() {
        const ctx = document.getElementById('sensorsChart').getContext('2d');
        sensorsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });
    }
    
    // Conectar WebSocket
    function connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/dashboard/`;
        
        websocket = new WebSocket(wsUrl);
        
        websocket.onopen = function(event) {
            console.log('WebSocket conectado');
            updateConnectionStatus(true);
        };
        
        websocket.onmessage = function(event) {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
        };
        
        websocket.onclose = function(event) {
            console.log('WebSocket desconectado');
            updateConnectionStatus(false);
            // Reconectar después de 5 segundos
            setTimeout(connectWebSocket, 5000);
        };
        
        websocket.onerror = function(error) {
            console.error('Error WebSocket:', error);
        };
    }
    
    // Manejar mensajes WebSocket
    function handleWebSocketMessage(data) {
        switch(data.type) {
            case 'dashboard_update':
                updateDashboardMetrics(data.data);
                break;
            case 'sensor_reading':
                updateSensorChart(data.data);
                updateCriticalSensors();
                break;
            case 'actuator_event':
                loadActuators(); // Recargar actuadores
                break;
        }
    }
    
    // Actualizar métricas del dashboard
    function updateDashboardMetrics(data) {
        document.getElementById('sectores-count').textContent = data.sectores_activos;
        document.getElementById('sensores-count').textContent = data.sensores_activos;
        document.getElementById('actuadores-count').textContent = data.actuadores_activos;
        document.getElementById('lecturas-hoy').textContent = data.lecturas_hoy;
        
        // Actualizar estado Arduino
        const indicator = document.querySelector('.status-indicator');
        const statusText = indicator.nextElementSibling;
        
        if (data.arduino_conectado) {
            indicator.className = 'status-indicator status-online';
            statusText.textContent = 'Arduino Conectado';
        } else {
            indicator.className = 'status-indicator status-offline';
            statusText.textContent = 'Arduino Desconectado';
        }
    }
    
    // Actualizar gráfico de sensores
    function updateSensorChart(sensorData) {
        // Implementar lógica para actualizar el gráfico
        // con nuevas lecturas de sensores
    }
    
    // Cargar actuadores
    function loadActuators() {
        fetch('/core/api/actuadores/?activo=true')
            .then(response => response.json())
            .then(data => {
                const panel = document.getElementById('actuators-panel');
                panel.innerHTML = '';
                
                data.results.forEach(actuator => {
                    const card = createActuatorCard(actuator);
                    panel.appendChild(card);
                });
            })
            .catch(error => console.error('Error cargando actuadores:', error));
    }
    
    // Crear tarjeta de actuador
    function createActuatorCard(actuator) {
        const div = document.createElement('div');
        div.className = 'actuator-control';
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">${actuator.nombre}</h6>
                    <small class="text-muted">${actuator.sector_nombre}</small>
                </div>
                <button class="btn btn-actuator ${actuator.estado_actual ? 'active' : 'inactive'}" 
                        onclick="toggleActuator(${actuator.id}, ${!actuator.estado_actual})">
                    ${actuator.estado_actual ? 'ON' : 'OFF'}
                </button>
            </div>
        `;
        return div;
    }
    
    // Controlar actuador
    function toggleActuator(actuatorId, newState) {
        fetch(`/core/api/actuadores/${actuatorId}/controlar/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                estado: newState,
                descripcion: `Control desde dashboard por ${getCurrentUser()}`
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                showNotification('success', data.message);
                loadActuators(); // Recargar actuadores
            } else if (data.error) {
                showNotification('error', data.error);
            }
        })
        .catch(error => {
            console.error('Error controlando actuador:', error);
            showNotification('error', 'Error controlando actuador');
        });
    }
    
    // Cargar sensores críticos
    function loadCriticalSensors() {
        // Implementar carga de sensores críticos
    }
    
    // Actualizar estado de conexión
    function updateConnectionStatus(connected) {
        // Implementar indicador de conexión WebSocket
    }
    
    // Refrescar dashboard
    function refreshDashboard() {
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify({type: 'request_update'}));
        }
        loadActuators();
        loadCriticalSensors();
    }
    
    // Utilidades
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    function getCurrentUser() {
        return '{{ user.get_full_name|default:user.username }}';
    }
    
    function showNotification(type, message) {
        // Implementar sistema de notificaciones
        console.log(`${type}: ${message}`);
    }
</script>
{% endblock %}
