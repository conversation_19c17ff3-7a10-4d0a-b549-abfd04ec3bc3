{% extends 'base.html' %}
{% load static %}

{% block title %}Programas de Riego - Sistema de Riego{% endblock %}

{% block extra_css %}
<style>
    .program-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        border-left: 5px solid #6f42c1;
    }
    
    .program-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .program-card.active {
        border-left-color: #28a745;
        background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
    }
    
    .program-card.inactive {
        border-left-color: #6c757d;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }
    
    .program-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .schedule-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .days-week {
        display: flex;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .day-badge {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: bold;
        color: white;
    }
    
    .day-active {
        background-color: #007bff;
    }
    
    .day-inactive {
        background-color: #dee2e6;
        color: #6c757d;
    }
    
    .next-execution {
        background: linear-gradient(45deg, #e3f2fd, #ffffff);
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 0.75rem;
        margin-top: 1rem;
    }
    
    .creation-notice {
        background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
        border: 2px dashed #ffc107;
        border-radius: 15px;
        padding: 3rem;
        text-align: center;
        margin: 2rem 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">
                    <i class="fas fa-clock text-purple me-2"></i>
                    Programas de Riego Automático
                </h1>
                <button class="btn btn-primary" onclick="showCreateProgram()">
                    <i class="fas fa-plus me-2"></i>Nuevo Programa
                </button>
            </div>
        </div>
    </div>

    <!-- Información general -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{{ programas|length }}</h3>
                    <small>Programas Totales</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">{% for programa in programas %}{% if programa.activo %}{{ forloop.counter }}{% endif %}{% endfor %}</h3>
                    <small>Programas Activos</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3 class="mb-1">--</h3>
                    <small>Próxima Ejecución</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Aviso de funcionalidad en desarrollo -->
    <div class="creation-notice">
        <i class="fas fa-tools fa-3x text-warning mb-3"></i>
        <h4 class="text-warning mb-3">Funcionalidad en Desarrollo</h4>
        <p class="text-muted mb-3">
            La gestión completa de programas de riego automático está siendo desarrollada. 
            Actualmente puedes ver los programas existentes, pero las funciones de creación, 
            edición y ejecución automática estarán disponibles próximamente.
        </p>
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="text-center">
                    <i class="fas fa-calendar-plus fa-2x text-info mb-2"></i>
                    <h6>Programación Avanzada</h6>
                    <small class="text-muted">Horarios personalizados por día</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <i class="fas fa-brain fa-2x text-success mb-2"></i>
                    <h6>Riego Inteligente</h6>
                    <small class="text-muted">Basado en sensores de humedad</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <i class="fas fa-mobile-alt fa-2x text-primary mb-2"></i>
                    <h6>Notificaciones</h6>
                    <small class="text-muted">Alertas en tiempo real</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de programas existentes -->
    {% if programas %}
    <div class="row">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-list me-2"></i>
                Programas Configurados
            </h4>
        </div>
    </div>

    <div class="row">
        {% for programa in programas %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="program-card {% if programa.activo %}active{% else %}inactive{% endif %}">
                <!-- Header del programa -->
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5 class="mb-1">{{ programa.nombre }}</h5>
                        <p class="text-muted mb-0">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            {{ programa.sector.nombre }}
                        </p>
                    </div>
                    <span class="program-status {% if programa.activo %}status-active{% else %}status-inactive{% endif %}">
                        {% if programa.activo %}Activo{% else %}Inactivo{% endif %}
                    </span>
                </div>

                <!-- Información de programación -->
                <div class="schedule-info">
                    <div class="row">
                        <div class="col-6">
                            <strong class="text-primary">{{ programa.hora_inicio }}</strong>
                            <br><small class="text-muted">Hora de inicio</small>
                        </div>
                        <div class="col-6">
                            <strong class="text-success">{{ programa.duracion_minutos }} min</strong>
                            <br><small class="text-muted">Duración</small>
                        </div>
                    </div>
                    
                    <!-- Días de la semana -->
                    <div class="mt-3">
                        <small class="text-muted d-block mb-2">Días de ejecución:</small>
                        <div class="days-week">
                            {% for day in "1234567" %}
                            <div class="day-badge {% if day in programa.dias_semana %}day-active{% else %}day-inactive{% endif %}">
                                {% if day == "1" %}L
                                {% elif day == "2" %}M
                                {% elif day == "3" %}X
                                {% elif day == "4" %}J
                                {% elif day == "5" %}V
                                {% elif day == "6" %}S
                                {% else %}D{% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Configuración de sensores -->
                {% if programa.usar_sensor_humedad %}
                <div class="mb-3">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-tint text-info me-2"></i>
                        <span class="small">
                            Riego inteligente: Solo si humedad < {{ programa.humedad_minima_activacion }}%
                        </span>
                    </div>
                </div>
                {% endif %}

                <!-- Próxima ejecución -->
                <div class="next-execution">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock text-primary me-2"></i>
                        <div>
                            <strong class="text-primary">Próxima ejecución</strong>
                            <br><small class="text-muted">En desarrollo - Cálculo automático</small>
                        </div>
                    </div>
                </div>

                <!-- Acciones -->
                <div class="d-flex justify-content-between align-items-center mt-3 pt-3 border-top">
                    <button class="btn btn-outline-primary btn-sm" onclick="showProgramDetails({{ programa.id }})">
                        <i class="fas fa-eye me-1"></i>Ver Detalles
                    </button>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-secondary btn-sm" onclick="editProgram({{ programa.id }})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-{% if programa.activo %}warning{% else %}success{% endif %} btn-sm" 
                                onclick="toggleProgram({{ programa.id }}, {{ programa.activo|yesno:'false,true' }})">
                            <i class="fas fa-{% if programa.activo %}pause{% else %}play{% endif %}"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No hay programas de riego configurados</h5>
                <p class="text-muted">Los programas automáticos aparecerán aquí cuando se configuren</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    function showCreateProgram() {
        showNotification('info', 'Función de creación de programas en desarrollo. Próximamente disponible.');
    }
    
    function showProgramDetails(programId) {
        showNotification('info', 'Vista de detalles del programa en desarrollo.');
    }
    
    function editProgram(programId) {
        showNotification('info', 'Editor de programas en desarrollo. Próximamente disponible.');
    }
    
    function toggleProgram(programId, newState) {
        const action = newState ? 'activar' : 'desactivar';
        showNotification('info', `Función para ${action} programa en desarrollo.`);
    }
    
    function showNotification(type, message) {
        alert(message);
    }
</script>
{% endblock %}
