{% extends 'base.html' %}
{% load static %}

{% block title %}Sectores - Sistema de Riego{% endblock %}

{% block extra_css %}
<style>
    .sector-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        border-left: 5px solid #007bff;
    }
    
    .sector-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .sector-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .sector-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .humidity-indicator {
        width: 100%;
        height: 8px;
        background-color: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin: 0.5rem 0;
    }
    
    .humidity-bar {
        height: 100%;
        border-radius: 4px;
        transition: width 0.3s ease;
    }
    
    .humidity-low {
        background: linear-gradient(90deg, #dc3545, #fd7e14);
    }
    
    .humidity-medium {
        background: linear-gradient(90deg, #ffc107, #28a745);
    }
    
    .humidity-high {
        background: linear-gradient(90deg, #28a745, #20c997);
    }
    
    .valve-control {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .valve-status {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
    }
    
    .valve-on {
        background-color: #28a745;
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
    
    .valve-off {
        background-color: #6c757d;
    }
    
    .btn-irrigation {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        color: white;
        border-radius: 25px;
        padding: 0.5rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-irrigation:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        color: white;
    }
    
    .sector-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .metric-item {
        text-align: center;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 8px;
    }
    
    .metric-value {
        font-size: 1.2rem;
        font-weight: bold;
        color: #007bff;
    }
    
    .metric-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">
                    <i class="fas fa-map-marked-alt text-primary me-2"></i>
                    Gestión de Sectores
                </h1>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSectorModal">
                    <i class="fas fa-plus me-2"></i>Nuevo Sector
                </button>
            </div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchSector" placeholder="Buscar sector...">
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="filterStatus">
                <option value="">Todos los estados</option>
                <option value="active">Solo activos</option>
                <option value="inactive">Solo inactivos</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="filterCrop">
                <option value="">Todos los cultivos</option>
                <option value="tomate">Tomate</option>
                <option value="lechuga">Lechuga</option>
                <option value="pimiento">Pimiento</option>
                <option value="otro">Otro</option>
            </select>
        </div>
    </div>

    <!-- Lista de sectores -->
    <div class="row" id="sectores-container">
        {% for sector in sectores %}
        <div class="col-lg-6 col-xl-4 sector-item" 
             data-name="{{ sector.nombre|lower }}" 
             data-status="{% if sector.activo %}active{% else %}inactive{% endif %}"
             data-crop="{{ sector.tipo_cultivo|lower }}">
            <div class="sector-card">
                <!-- Header del sector -->
                <div class="sector-header">
                    <div>
                        <h5 class="mb-1">{{ sector.nombre }}</h5>
                        <small class="text-muted">{{ sector.tipo_cultivo|title }} - {{ sector.area_m2 }}m²</small>
                    </div>
                    <div class="sector-status">
                        <span class="status-badge {% if sector.activo %}status-active{% else %}status-inactive{% endif %}">
                            {% if sector.activo %}Activo{% else %}Inactivo{% endif %}
                        </span>
                    </div>
                </div>

                <!-- Información de humedad -->
                {% if sector.humedad_actual is not None %}
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <small class="text-muted">Humedad del suelo</small>
                        <small class="fw-bold">{{ sector.humedad_actual|floatformat:1 }}%</small>
                    </div>
                    <div class="humidity-indicator">
                        <div class="humidity-bar 
                            {% if sector.humedad_actual < 30 %}humidity-low
                            {% elif sector.humedad_actual < 70 %}humidity-medium
                            {% else %}humidity-high{% endif %}"
                            style="width: {{ sector.humedad_actual }}%">
                        </div>
                    </div>
                    {% if sector.humedad_actual < sector.humedad_minima %}
                    <small class="text-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Humedad por debajo del mínimo ({{ sector.humedad_minima }}%)
                    </small>
                    {% endif %}
                </div>
                {% else %}
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-exclamation-circle me-1"></i>
                        Sin datos de humedad
                    </small>
                </div>
                {% endif %}

                <!-- Control de válvula -->
                <div class="valve-control mb-3">
                    <div class="d-flex align-items-center">
                        <span class="valve-status {% if sector.valvula_activa %}valve-on{% else %}valve-off{% endif %}"></span>
                        <span class="ms-2 small">
                            Válvula {% if sector.valvula_activa %}ABIERTA{% else %}CERRADA{% endif %}
                        </span>
                    </div>
                    <button class="btn btn-irrigation btn-sm" 
                            onclick="activateIrrigation({{ sector.id }}, '{{ sector.nombre }}')">
                        <i class="fas fa-tint me-1"></i>
                        Regar Ahora
                    </button>
                </div>

                <!-- Métricas del sector -->
                <div class="sector-metrics">
                    <div class="metric-item">
                        <div class="metric-value">{{ sector.sensores_count }}</div>
                        <div class="metric-label">Sensores</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{{ sector.actuadores_count }}</div>
                        <div class="metric-label">Actuadores</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-value">{{ sector.tiempo_riego_minutos }}</div>
                        <div class="metric-label">Min. Riego</div>
                    </div>
                </div>

                <!-- Acciones -->
                <div class="d-flex justify-content-between align-items-center mt-3 pt-3 border-top">
                    <a href="{% url 'dashboard:sector_detalle' sector.id %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>Ver Detalles
                    </a>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-secondary btn-sm" 
                                onclick="editSector({{ sector.id }})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-info btn-sm" 
                                onclick="viewHistory({{ sector.id }})">
                            <i class="fas fa-history"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No hay sectores configurados</h5>
                <p class="text-muted">Crea tu primer sector para comenzar</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSectorModal">
                    <i class="fas fa-plus me-2"></i>Crear Sector
                </button>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Modal para nuevo sector -->
<div class="modal fade" id="addSectorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nuevo Sector</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addSectorForm">
                    <div class="mb-3">
                        <label for="sectorName" class="form-label">Nombre del Sector</label>
                        <input type="text" class="form-control" id="sectorName" required>
                    </div>
                    <div class="mb-3">
                        <label for="sectorDescription" class="form-label">Descripción</label>
                        <textarea class="form-control" id="sectorDescription" rows="3"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="sectorArea" class="form-label">Área (m²)</label>
                            <input type="number" class="form-control" id="sectorArea" min="1" required>
                        </div>
                        <div class="col-md-6">
                            <label for="sectorCrop" class="form-label">Tipo de Cultivo</label>
                            <select class="form-select" id="sectorCrop" required>
                                <option value="">Seleccionar...</option>
                                <option value="tomate">Tomate</option>
                                <option value="lechuga">Lechuga</option>
                                <option value="pimiento">Pimiento</option>
                                <option value="otro">Otro</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="saveSector()">Guardar Sector</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Filtros
    document.getElementById('searchSector').addEventListener('input', filterSectors);
    document.getElementById('filterStatus').addEventListener('change', filterSectors);
    document.getElementById('filterCrop').addEventListener('change', filterSectors);
    
    function filterSectors() {
        const search = document.getElementById('searchSector').value.toLowerCase();
        const status = document.getElementById('filterStatus').value;
        const crop = document.getElementById('filterCrop').value;
        
        const sectors = document.querySelectorAll('.sector-item');
        
        sectors.forEach(sector => {
            const name = sector.dataset.name;
            const sectorStatus = sector.dataset.status;
            const sectorCrop = sector.dataset.crop;
            
            let show = true;
            
            if (search && !name.includes(search)) show = false;
            if (status && sectorStatus !== status) show = false;
            if (crop && sectorCrop !== crop) show = false;
            
            sector.style.display = show ? 'block' : 'none';
        });
    }
    
    // Activar riego
    function activateIrrigation(sectorId, sectorName) {
        if (confirm(`¿Activar riego para el sector "${sectorName}"?`)) {
            fetch(`/core/api/sectores/${sectorId}/activar_riego/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    duracion_minutos: 15 // Duración por defecto
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    showNotification('success', data.message);
                    setTimeout(() => location.reload(), 2000);
                } else if (data.error) {
                    showNotification('error', data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('error', 'Error activando riego');
            });
        }
    }
    
    // Guardar sector
    function saveSector() {
        const form = document.getElementById('addSectorForm');
        const formData = new FormData(form);
        
        // Implementar guardado de sector
        console.log('Guardando sector...');
    }
    
    // Utilidades
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    function showNotification(type, message) {
        // Implementar sistema de notificaciones
        alert(message);
    }
</script>
{% endblock %}
