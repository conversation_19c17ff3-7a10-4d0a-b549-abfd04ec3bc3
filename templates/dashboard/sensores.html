{% extends 'base.html' %}
{% load static %}

{% block title %}Sensores - Sistema de Riego{% endblock %}

{% block extra_css %}
<style>
    .sensor-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        border-left: 5px solid #28a745;
    }
    
    .sensor-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .sensor-value {
        font-size: 2rem;
        font-weight: bold;
        color: #28a745;
        margin-bottom: 0.5rem;
    }
    
    .sensor-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-normal {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-warning {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-danger {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .sensor-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }
    
    .icon-humidity {
        background: linear-gradient(45deg, #007bff, #0056b3);
    }
    
    .icon-temperature {
        background: linear-gradient(45deg, #dc3545, #c82333);
    }
    
    .icon-ph {
        background: linear-gradient(45deg, #ffc107, #e0a800);
    }
    
    .icon-conductivity {
        background: linear-gradient(45deg, #6f42c1, #5a32a3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">
                    <i class="fas fa-thermometer-half text-success me-2"></i>
                    Monitoreo de Sensores
                </h1>
                <button class="btn btn-success" onclick="refreshSensors()">
                    <i class="fas fa-sync-alt me-2"></i>Actualizar Lecturas
                </button>
            </div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" class="form-control" id="searchSensor" placeholder="Buscar sensor...">
            </div>
        </div>
        <div class="col-md-4">
            <select class="form-select" id="filterSector">
                <option value="">Todos los sectores</option>
                <option value="1">Sector A - Tomates</option>
                <option value="2">Sector B - Lechugas</option>
                <option value="3">Sector C - Pimientos</option>
            </select>
        </div>
        <div class="col-md-4">
            <select class="form-select" id="filterType">
                <option value="">Todos los tipos</option>
                <option value="humedad_suelo">Humedad del Suelo</option>
                <option value="temperatura">Temperatura</option>
                <option value="ph_suelo">pH del Suelo</option>
                <option value="conductividad">Conductividad</option>
            </select>
        </div>
    </div>

    <!-- Lista de sensores -->
    <div class="row" id="sensores-container">
        {% for sensor in sensores %}
        <div class="col-lg-4 col-md-6 mb-4 sensor-item" 
             data-name="{{ sensor.nombre|lower }}" 
             data-sector="{{ sensor.sector.id }}"
             data-type="{{ sensor.tipo }}">
            <div class="sensor-card">
                <!-- Icono del sensor -->
                <div class="sensor-icon 
                    {% if sensor.tipo == 'humedad_suelo' %}icon-humidity
                    {% elif sensor.tipo == 'temperatura' %}icon-temperature
                    {% elif sensor.tipo == 'ph_suelo' %}icon-ph
                    {% else %}icon-conductivity{% endif %}">
                    <i class="fas 
                        {% if sensor.tipo == 'humedad_suelo' %}fa-tint
                        {% elif sensor.tipo == 'temperatura' %}fa-thermometer-half
                        {% elif sensor.tipo == 'ph_suelo' %}fa-flask
                        {% else %}fa-bolt{% endif %}"></i>
                </div>

                <!-- Información del sensor -->
                <h5 class="mb-2">{{ sensor.nombre }}</h5>
                <p class="text-muted mb-3">
                    <i class="fas fa-map-marker-alt me-1"></i>
                    {{ sensor.sector.nombre }}
                </p>

                <!-- Valor actual -->
                {% if sensor.ultima_lectura %}
                <div class="sensor-value">
                    {{ sensor.ultima_lectura.valor_calibrado|floatformat:1 }}
                    <small class="text-muted">{{ sensor.unidad_medida }}</small>
                </div>
                <small class="text-muted">
                    Última lectura: {{ sensor.ultima_lectura.timestamp|date:"H:i" }}
                </small>

                <!-- Estado del sensor -->
                <div class="mt-3">
                    {% if sensor.ultima_lectura.valor_calibrado >= sensor.valor_minimo and sensor.ultima_lectura.valor_calibrado <= sensor.valor_maximo %}
                        <span class="sensor-status status-normal">Normal</span>
                    {% elif sensor.ultima_lectura.valor_calibrado < sensor.valor_minimo %}
                        <span class="sensor-status status-warning">Bajo</span>
                    {% else %}
                        <span class="sensor-status status-danger">Alto</span>
                    {% endif %}
                </div>
                {% else %}
                <div class="sensor-value text-muted">
                    --
                    <small>{{ sensor.unidad_medida }}</small>
                </div>
                <small class="text-muted">Sin datos disponibles</small>
                <div class="mt-3">
                    <span class="sensor-status status-warning">Sin datos</span>
                </div>
                {% endif %}

                <!-- Rango del sensor -->
                <div class="mt-3 pt-3 border-top">
                    <small class="text-muted">
                        Rango: {{ sensor.valor_minimo }} - {{ sensor.valor_maximo }} {{ sensor.unidad_medida }}
                    </small>
                </div>

                <!-- Acciones -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <button class="btn btn-outline-success btn-sm" 
                            onclick="readSensorNow({{ sensor.id }})">
                        <i class="fas fa-play me-1"></i>Leer Ahora
                    </button>
                    <button class="btn btn-outline-info btn-sm" 
                            onclick="viewSensorHistory({{ sensor.id }})">
                        <i class="fas fa-chart-line me-1"></i>Historial
                    </button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-thermometer-half fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No hay sensores configurados</h5>
                <p class="text-muted">Los sensores aparecerán aquí cuando se configuren</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Filtros
    document.getElementById('searchSensor').addEventListener('input', filterSensors);
    document.getElementById('filterSector').addEventListener('change', filterSensors);
    document.getElementById('filterType').addEventListener('change', filterSensors);
    
    function filterSensors() {
        const search = document.getElementById('searchSensor').value.toLowerCase();
        const sector = document.getElementById('filterSector').value;
        const type = document.getElementById('filterType').value;
        
        const sensors = document.querySelectorAll('.sensor-item');
        
        sensors.forEach(sensor => {
            const name = sensor.dataset.name;
            const sensorSector = sensor.dataset.sector;
            const sensorType = sensor.dataset.type;
            
            let show = true;
            
            if (search && !name.includes(search)) show = false;
            if (sector && sensorSector !== sector) show = false;
            if (type && sensorType !== type) show = false;
            
            sensor.style.display = show ? 'block' : 'none';
        });
    }
    
    // Leer sensor ahora
    function readSensorNow(sensorId) {
        fetch(`/core/api/sensores/${sensorId}/leer_ahora/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                showNotification('success', 'Lectura solicitada correctamente');
            } else if (data.error) {
                showNotification('error', data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('error', 'Error solicitando lectura');
        });
    }
    
    // Ver historial del sensor
    function viewSensorHistory(sensorId) {
        // Por ahora mostrar mensaje
        showNotification('info', 'Función de historial en desarrollo');
    }
    
    // Actualizar sensores
    function refreshSensors() {
        location.reload();
    }
    
    // Utilidades
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    function showNotification(type, message) {
        alert(message);
    }
</script>
{% endblock %}
