<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar <PERSON><PERSON><PERSON><PERSON><PERSON> Riego A<PERSON>r<PERSON></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2E7D32;
            --secondary-color: #4CAF50;
            --accent-color: #81C784;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-image {
            background: linear-gradient(rgba(46, 125, 50, 0.8), rgba(76, 175, 80, 0.8)), 
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="50" cy="50" r="2" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        .login-form {
            padding: 3rem;
        }
        
        .brand-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: white;
        }
        
        .brand-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .brand-subtitle {
            opacity: 0.9;
            font-size: 1rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: bold;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
        }
        
        .form-floating label {
            color: #6c757d;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .login-links {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .login-links a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-links a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .login-image {
                display: none;
            }
            .login-container {
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="login-container row g-0">
                    <!-- Left side - Image and branding -->
                    <div class="col-md-6 login-image">
                        <div>
                            <div class="brand-logo">
                                <i class="fas fa-seedling"></i>
                            </div>
                            <h2 class="brand-title">Sistema de Riego Agrícola</h2>
                            <p class="brand-subtitle">Automatización inteligente para tu cultivo</p>
                            <div class="mt-4">
                                <i class="fas fa-tint me-3"></i>
                                <i class="fas fa-thermometer-half me-3"></i>
                                <i class="fas fa-chart-line me-3"></i>
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Right side - Login form -->
                    <div class="col-md-6 login-form">
                        <div class="text-center mb-4">
                            <h3 class="text-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Iniciar Sesión
                            </h3>
                            <p class="text-muted">Accede a tu panel de control</p>
                        </div>
                        
                        <!-- Messages -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                        
                        <form method="post">
                            {% csrf_token %}
                            
                            <div class="form-floating mb-3">
                                {{ form.username }}
                                <label for="{{ form.username.id_for_label }}">
                                    <i class="fas fa-user me-2"></i>{{ form.username.label }}
                                </label>
                            </div>
                            
                            <div class="form-floating mb-4">
                                {{ form.password }}
                                <label for="{{ form.password.id_for_label }}">
                                    <i class="fas fa-lock me-2"></i>{{ form.password.label }}
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Iniciar Sesión
                                </button>
                            </div>
                        </form>
                        
                        <div class="login-links">
                            <p class="mb-2">¿No tienes cuenta?</p>
                            <a href="{% url 'users:registro' %}">
                                <i class="fas fa-user-plus me-1"></i>
                                Crear cuenta nueva
                            </a>
                        </div>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-shield-alt me-1"></i>
                                Conexión segura SSL
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
