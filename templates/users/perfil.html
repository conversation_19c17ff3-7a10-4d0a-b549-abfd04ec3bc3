{% extends 'base.html' %}

{% block title %}Mi Perfil - Sistema de Riego Agrícola{% endblock %}
{% block page_title %}Mi Perfil{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    Información del Perfil
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-user me-2"></i>Nombre de Usuario
                                </label>
                                <input type="text" class="form-control" value="{{ user.username }}" readonly>
                                <small class="text-muted">El nombre de usuario no se puede cambiar</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-envelope me-2"></i>Correo Electrónico
                                </label>
                                <input type="email" class="form-control" value="{{ user.email }}" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-id-card me-2"></i>Nombre Completo
                                </label>
                                <input type="text" class="form-control" value="{{ user.get_full_name }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-user-tag me-2"></i>Rol
                                </label>
                                <input type="text" class="form-control" value="{{ perfil.get_rol_display }}" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.telefono.id_for_label }}" class="form-label">
                                    <i class="fas fa-phone me-2"></i>{{ form.telefono.label }}
                                </label>
                                {{ form.telefono }}
                                {% if form.telefono.errors %}
                                    <div class="text-danger small">{{ form.telefono.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.avatar.id_for_label }}" class="form-label">
                                    <i class="fas fa-image me-2"></i>{{ form.avatar.label }}
                                </label>
                                {{ form.avatar }}
                                {% if form.avatar.errors %}
                                    <div class="text-danger small">{{ form.avatar.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-bell me-2"></i>
                                Preferencias de Notificaciones
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        {{ form.recibir_notificaciones }}
                                        <label class="form-check-label" for="{{ form.recibir_notificaciones.id_for_label }}">
                                            {{ form.recibir_notificaciones.label }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        {{ form.notificaciones_email }}
                                        <label class="form-check-label" for="{{ form.notificaciones_email.id_for_label }}">
                                            {{ form.notificaciones_email.label }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        {{ form.notificaciones_sms }}
                                        <label class="form-check-label" for="{{ form.notificaciones_sms.id_for_label }}">
                                            {{ form.notificaciones_sms.label }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Guardar Cambios
                        </button>
                        <a href="{% url 'dashboard:home' %}" class="btn btn-secondary ms-2">
                            <i class="fas fa-arrow-left me-2"></i>
                            Volver al Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Avatar Preview -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user-circle me-2"></i>
                    Foto de Perfil
                </h6>
            </div>
            <div class="card-body text-center">
                {% if perfil.avatar %}
                    <img src="{{ perfil.avatar.url }}" alt="Avatar" class="img-fluid rounded-circle mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                {% else %}
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 150px; height: 150px;">
                        <i class="fas fa-user text-white" style="font-size: 4rem;"></i>
                    </div>
                {% endif %}
                <h5>{{ user.get_full_name|default:user.username }}</h5>
                <p class="text-muted">{{ perfil.get_rol_display }}</p>
            </div>
        </div>
        
        <!-- Account Info -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Información de la Cuenta
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">Miembro desde:</small>
                    <div>{{ user.date_joined|date:"d/m/Y" }}</div>
                </div>
                
                {% if perfil.ultimo_acceso %}
                <div class="mb-3">
                    <small class="text-muted">Último acceso:</small>
                    <div>{{ perfil.ultimo_acceso|date:"d/m/Y H:i" }}</div>
                </div>
                {% endif %}
                
                {% if perfil.sectores_asignados.exists %}
                <div class="mb-3">
                    <small class="text-muted">Sectores asignados:</small>
                    <div>
                        {% for sector in perfil.sectores_asignados.all %}
                            <span class="badge bg-success me-1">{{ sector.nombre }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <div class="mb-0">
                    <small class="text-muted">Estado de notificaciones:</small>
                    <div>
                        {% if perfil.recibir_notificaciones %}
                            <span class="badge bg-success">
                                <i class="fas fa-bell me-1"></i>Activas
                            </span>
                        {% else %}
                            <span class="badge bg-secondary">
                                <i class="fas fa-bell-slash me-1"></i>Desactivadas
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
