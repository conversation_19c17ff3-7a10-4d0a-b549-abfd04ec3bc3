<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro - Sistema de Riego Agrícola</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2E7D32;
            --secondary-color: #4CAF50;
            --accent-color: #81C784;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
            padding: 2rem 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
            margin: 0 auto;
        }
        
        .register-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .register-form {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: bold;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
        }
        
        .form-floating label {
            color: #6c757d;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .register-links {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .register-links a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .register-links a:hover {
            text-decoration: underline;
        }
        
        .password-requirements {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }
        
        .password-requirements ul {
            margin: 0;
            padding-left: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="register-container">
            <!-- Header -->
            <div class="register-header">
                <div class="mb-3">
                    <i class="fas fa-seedling" style="font-size: 2.5rem;"></i>
                </div>
                <h2>Crear Cuenta Nueva</h2>
                <p class="mb-0">Únete al Sistema de Riego Agrícola</p>
            </div>
            
            <!-- Form -->
            <div class="register-form">
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                {{ form.first_name }}
                                <label for="{{ form.first_name.id_for_label }}">
                                    <i class="fas fa-user me-2"></i>{{ form.first_name.label }}
                                </label>
                                {% if form.first_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.first_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                {{ form.last_name }}
                                <label for="{{ form.last_name.id_for_label }}">
                                    <i class="fas fa-user me-2"></i>{{ form.last_name.label }}
                                </label>
                                {% if form.last_name.errors %}
                                    <div class="text-danger small mt-1">
                                        {{ form.last_name.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-floating mb-3">
                        {{ form.username }}
                        <label for="{{ form.username.id_for_label }}">
                            <i class="fas fa-at me-2"></i>{{ form.username.label }}
                        </label>
                        {% if form.username.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.username.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-floating mb-3">
                        {{ form.email }}
                        <label for="{{ form.email.id_for_label }}">
                            <i class="fas fa-envelope me-2"></i>{{ form.email.label }}
                        </label>
                        {% if form.email.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.email.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-floating mb-3">
                        {{ form.password1 }}
                        <label for="{{ form.password1.id_for_label }}">
                            <i class="fas fa-lock me-2"></i>{{ form.password1.label }}
                        </label>
                        {% if form.password1.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.password1.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-floating mb-3">
                        {{ form.password2 }}
                        <label for="{{ form.password2.id_for_label }}">
                            <i class="fas fa-lock me-2"></i>{{ form.password2.label }}
                        </label>
                        {% if form.password2.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.password2.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="password-requirements">
                        <strong>Requisitos de la contraseña:</strong>
                        <ul>
                            <li>Mínimo 8 caracteres</li>
                            <li>No puede ser muy común</li>
                            <li>No puede ser solo números</li>
                        </ul>
                    </div>
                    
                    <div class="d-grid mt-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>
                            Crear Cuenta
                        </button>
                    </div>
                </form>
                
                <div class="register-links">
                    <p class="mb-2">¿Ya tienes cuenta?</p>
                    <a href="{% url 'users:login' %}">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        Iniciar sesión
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
