from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from .models import PerfilUsuario, SesionUsuario, NotificacionUsuario


class PerfilUsuarioInline(admin.StackedInline):
    model = PerfilUsuario
    can_delete = False
    verbose_name_plural = 'Perfil'


class UserAdmin(BaseUserAdmin):
    inlines = (PerfilUsuarioInline,)


# Re-register UserAdmin
admin.site.unregister(User)
admin.site.register(User, UserAdmin)


@admin.register(SesionUsuario)
class SesionUsuarioAdmin(admin.ModelAdmin):
    list_display = ['user', 'ip_address', 'inicio_sesion', 'fin_sesion', 'activa']
    list_filter = ['activa', 'inicio_sesion']
    search_fields = ['user__username', 'ip_address']
    ordering = ['-inicio_sesion']
    readonly_fields = ['inicio_sesion', 'fin_sesion']


@admin.register(NotificacionUsuario)
class NotificacionUsuarioAdmin(admin.ModelAdmin):
    list_display = ['titulo', 'usuario', 'tipo', 'prioridad', 'leida', 'created_at']
    list_filter = ['tipo', 'prioridad', 'leida', 'created_at']
    search_fields = ['titulo', 'mensaje', 'usuario__username']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'leida_at']
