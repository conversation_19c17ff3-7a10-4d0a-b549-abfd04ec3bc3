# Generated by Django 4.2.7 on 2025-09-20 14:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SesionUsuario',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('inicio_sesion', models.DateTimeField(auto_now_add=True)),
                ('fin_sesion', models.DateTimeField(blank=True, null=True)),
                ('activa', models.BooleanField(default=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sesiones', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sesión de Usuario',
                'verbose_name_plural': 'Sesiones de Usuarios',
                'ordering': ['-inicio_sesion'],
            },
        ),
        migrations.CreateModel(
            name='PerfilUsuario',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rol', models.CharField(choices=[('admin', 'Administrador'), ('operador', 'Operador'), ('visualizador', 'Solo Visualización')], default='visualizador', max_length=20)),
                ('telefono', models.CharField(blank=True, max_length=20)),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/')),
                ('recibir_notificaciones', models.BooleanField(default=True)),
                ('notificaciones_email', models.BooleanField(default=True)),
                ('notificaciones_sms', models.BooleanField(default=False)),
                ('ultimo_acceso', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sectores_asignados', models.ManyToManyField(blank=True, help_text='Sectores que puede gestionar este usuario', to='core.sector')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='perfil', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Perfil de Usuario',
                'verbose_name_plural': 'Perfiles de Usuarios',
            },
        ),
        migrations.CreateModel(
            name='NotificacionUsuario',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('titulo', models.CharField(max_length=200)),
                ('mensaje', models.TextField()),
                ('tipo', models.CharField(choices=[('info', 'Información'), ('warning', 'Advertencia'), ('error', 'Error'), ('success', 'Éxito')], default='info', max_length=20)),
                ('prioridad', models.CharField(choices=[('baja', 'Baja'), ('media', 'Media'), ('alta', 'Alta'), ('critica', 'Crítica')], default='media', max_length=20)),
                ('leida', models.BooleanField(default=False)),
                ('enviada_email', models.BooleanField(default=False)),
                ('enviada_sms', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('leida_at', models.DateTimeField(blank=True, null=True)),
                ('sector_relacionado', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.sector')),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notificaciones', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notificación',
                'verbose_name_plural': 'Notificaciones',
                'ordering': ['-created_at'],
            },
        ),
    ]
