from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class PerfilUsuario(models.Model):
    """Perfil extendido para usuarios del sistema"""
    ROLES_CHOICES = [
        ('admin', 'Administrador'),
        ('operador', 'Operador'),
        ('visualizador', 'Solo Visualización'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='perfil')
    rol = models.CharField(max_length=20, choices=ROLES_CHOICES, default='visualizador')
    telefono = models.CharField(max_length=20, blank=True)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    recibir_notificaciones = models.BooleanField(default=True)
    notificaciones_email = models.BooleanField(default=True)
    notificaciones_sms = models.BooleanField(default=False)
    sectores_asignados = models.ManyToManyField(
        'core.Sector',
        blank=True,
        help_text="Sectores que puede gestionar este usuario"
    )
    ultimo_acceso = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Perfil de Usuario"
        verbose_name_plural = "Perfiles de Usuarios"

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} ({self.get_rol_display()})"

    def puede_controlar_sector(self, sector):
        """Verifica si el usuario puede controlar un sector específico"""
        if self.rol == 'admin':
            return True
        return self.sectores_asignados.filter(id=sector.id).exists()


class SesionUsuario(models.Model):
    """Modelo para rastrear sesiones de usuarios"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sesiones')
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    inicio_sesion = models.DateTimeField(auto_now_add=True)
    fin_sesion = models.DateTimeField(null=True, blank=True)
    activa = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Sesión de Usuario"
        verbose_name_plural = "Sesiones de Usuarios"
        ordering = ['-inicio_sesion']

    def __str__(self):
        return f"{self.user.username} - {self.inicio_sesion}"

    def duracion_sesion(self):
        """Calcula la duración de la sesión"""
        fin = self.fin_sesion or timezone.now()
        return fin - self.inicio_sesion


class NotificacionUsuario(models.Model):
    """Modelo para notificaciones a usuarios"""
    TIPO_NOTIFICACION_CHOICES = [
        ('info', 'Información'),
        ('warning', 'Advertencia'),
        ('error', 'Error'),
        ('success', 'Éxito'),
    ]

    PRIORIDAD_CHOICES = [
        ('baja', 'Baja'),
        ('media', 'Media'),
        ('alta', 'Alta'),
        ('critica', 'Crítica'),
    ]

    usuario = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notificaciones')
    titulo = models.CharField(max_length=200)
    mensaje = models.TextField()
    tipo = models.CharField(max_length=20, choices=TIPO_NOTIFICACION_CHOICES, default='info')
    prioridad = models.CharField(max_length=20, choices=PRIORIDAD_CHOICES, default='media')
    leida = models.BooleanField(default=False)
    enviada_email = models.BooleanField(default=False)
    enviada_sms = models.BooleanField(default=False)
    sector_relacionado = models.ForeignKey(
        'core.Sector',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    leida_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Notificación"
        verbose_name_plural = "Notificaciones"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.titulo} - {self.usuario.username}"

    def marcar_como_leida(self):
        """Marca la notificación como leída"""
        if not self.leida:
            self.leida = True
            self.leida_at = timezone.now()
            self.save()
