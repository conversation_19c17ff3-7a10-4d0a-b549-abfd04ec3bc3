from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from .models import PerfilUsuario, NotificacionUsuario
from .forms import RegistroForm, PerfilForm, LoginForm
import json


def login_view(request):
    """Vista para login de usuarios"""
    if request.user.is_authenticated:
        return redirect('dashboard:home')

    if request.method == 'POST':
        form = LoginForm(request.POST)
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            user = authenticate(request, username=username, password=password)

            if user is not None:
                login(request, user)
                # Actualizar último acceso
                if hasattr(user, 'perfil'):
                    user.perfil.ultimo_acceso = timezone.now()
                    user.perfil.save()

                messages.success(request, f'¡Bienvenido {user.get_full_name() or user.username}!')
                next_url = request.GET.get('next', 'dashboard:home')
                return redirect(next_url)
            else:
                messages.error(request, 'Usuario o contraseña incorrectos.')
    else:
        form = LoginForm()

    return render(request, 'users/login.html', {'form': form})


def logout_view(request):
    """Vista para logout de usuarios"""
    logout(request)
    messages.info(request, 'Has cerrado sesión correctamente.')
    return redirect('users:login')


def registro_view(request):
    """Vista para registro de nuevos usuarios"""
    if request.method == 'POST':
        form = RegistroForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Crear perfil automáticamente
            PerfilUsuario.objects.create(user=user, rol='visualizador')

            messages.success(request, 'Cuenta creada exitosamente. Ya puedes iniciar sesión.')
            return redirect('users:login')
    else:
        form = RegistroForm()

    return render(request, 'users/registro.html', {'form': form})


@login_required
def perfil_view(request):
    """Vista para ver y editar perfil de usuario"""
    perfil, created = PerfilUsuario.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        form = PerfilForm(request.POST, request.FILES, instance=perfil)
        if form.is_valid():
            form.save()
            messages.success(request, 'Perfil actualizado correctamente.')
            return redirect('users:perfil')
    else:
        form = PerfilForm(instance=perfil)

    return render(request, 'users/perfil.html', {
        'form': form,
        'perfil': perfil
    })


@login_required
@require_http_methods(["GET"])
def notificaciones_view(request):
    """Vista para mostrar notificaciones del usuario"""
    notificaciones = NotificacionUsuario.objects.filter(
        usuario=request.user
    ).order_by('-created_at')[:50]

    # Marcar notificaciones como leídas si se solicita
    if request.GET.get('marcar_leidas'):
        notificaciones.filter(leida=False).update(
            leida=True,
            leida_at=timezone.now()
        )
        return JsonResponse({'status': 'success'})

    return render(request, 'users/notificaciones.html', {
        'notificaciones': notificaciones
    })


@login_required
@require_http_methods(["POST"])
def marcar_notificacion_leida(request, notificacion_id):
    """Vista AJAX para marcar una notificación como leída"""
    try:
        notificacion = get_object_or_404(
            NotificacionUsuario,
            id=notificacion_id,
            usuario=request.user
        )
        notificacion.marcar_como_leida()
        return JsonResponse({'status': 'success'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})


@login_required
def dashboard_data(request):
    """Vista AJAX para datos del dashboard del usuario"""
    perfil = get_object_or_404(PerfilUsuario, user=request.user)

    # Obtener sectores asignados
    sectores = perfil.sectores_asignados.all() if perfil.rol != 'admin' else []

    # Notificaciones no leídas
    notificaciones_no_leidas = NotificacionUsuario.objects.filter(
        usuario=request.user,
        leida=False
    ).count()

    data = {
        'user': {
            'username': request.user.username,
            'full_name': request.user.get_full_name(),
            'rol': perfil.get_rol_display(),
            'ultimo_acceso': perfil.ultimo_acceso.isoformat() if perfil.ultimo_acceso else None,
        },
        'sectores_asignados': [
            {
                'id': sector.id,
                'nombre': sector.nombre,
                'tipo_cultivo': sector.tipo_cultivo,
                'activo': sector.activo
            } for sector in sectores
        ],
        'notificaciones_no_leidas': notificaciones_no_leidas,
        'permisos': {
            'puede_controlar_actuadores': perfil.rol in ['admin', 'operador'],
            'puede_ver_configuracion': perfil.rol == 'admin',
            'puede_gestionar_usuarios': perfil.rol == 'admin',
        }
    }

    return JsonResponse(data)
