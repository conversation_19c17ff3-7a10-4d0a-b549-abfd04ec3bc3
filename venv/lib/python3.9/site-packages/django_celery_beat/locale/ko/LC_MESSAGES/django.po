# Korean translation strings for django-celery-beat
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2022.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-22 19:03+0000\n"
"PO-Revision-Date: 2022-10-14 23:48+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: ko\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: django_celery_beat/admin.py:60
msgid "Task (registered)"
msgstr "등록 태스크 목록"

#: django_celery_beat/admin.py:64
msgid "Task (custom)"
msgstr "사용자 정의 태스크"

#: django_celery_beat/admin.py:81
msgid "Need name of task"
msgstr "태스크 이름이 필요 합니다."

#: django_celery_beat/admin.py:87 django_celery_beat/models.py:605
msgid "Only one can be set, in expires and expire_seconds"
msgstr "'만료 시각'과 '만료 기준 초' 중에 하나만 지정할 수 있습니다."

#: django_celery_beat/admin.py:97
#, python-format
msgid "Unable to parse JSON: %s"
msgstr "해석할 수 없습니다. JSON: %s"

#: django_celery_beat/admin.py:125
#, fuzzy
#| msgid "Solar Schedule"
msgid "Schedule"
msgstr "Solar Schedule"

#: django_celery_beat/admin.py:130
#, fuzzy
#| msgid "Keyword Arguments"
msgid "Arguments"
msgstr "Keyword Arguments"

#: django_celery_beat/admin.py:134
msgid "Execution Options"
msgstr ""

#: django_celery_beat/admin.py:177
#, python-brace-format
msgid "{0} task{1} {2} successfully {3}"
msgstr "{0} 태스크{1} {2} 성공적으로 {3}"

#: django_celery_beat/admin.py:180 django_celery_beat/admin.py:247
msgid "was,were"
msgstr "가,들이"

#: django_celery_beat/admin.py:189
msgid "Enable selected tasks"
msgstr "선택된 태스크들을 활성화 합니다."

#: django_celery_beat/admin.py:195
msgid "Disable selected tasks"
msgstr "선택된 태스크들을 비활성화 합니다."

#: django_celery_beat/admin.py:207
msgid "Toggle activity of selected tasks"
msgstr "선택된 태스크들의 활성화 상태를 토글 합니다."

#: django_celery_beat/admin.py:228
#, fuzzy, python-brace-format
#| msgid "task \"{0}\" not found"
msgid "task \"{not_found_task_name}\" not found"
msgstr "태스크 \"{0}\" 을 찾을 수 없습니다."

#: django_celery_beat/admin.py:244
#, python-brace-format
msgid "{0} task{1} {2} successfully run"
msgstr "{0} 태스크{1} {2} 성공적으로 작동했습니다."

#: django_celery_beat/admin.py:250
msgid "Run selected tasks"
msgstr "선택된 태스크들을 실행 합니다."

#: django_celery_beat/apps.py:13
msgid "Periodic Tasks"
msgstr "주기적인 태스크들"

#: django_celery_beat/models.py:30
msgid "Days"
msgstr "일"

#: django_celery_beat/models.py:31
msgid "Hours"
msgstr "시간"

#: django_celery_beat/models.py:32
msgid "Minutes"
msgstr "분"

#: django_celery_beat/models.py:33
msgid "Seconds"
msgstr "초"

#: django_celery_beat/models.py:34
msgid "Microseconds"
msgstr "마이크로초"

#: django_celery_beat/models.py:38
msgid "Day"
msgstr "일"

#: django_celery_beat/models.py:39
msgid "Hour"
msgstr "시"

#: django_celery_beat/models.py:40
msgid "Minute"
msgstr "분"

#: django_celery_beat/models.py:41
msgid "Second"
msgstr "초"

#: django_celery_beat/models.py:42
msgid "Microsecond"
msgstr "마이크로초"

#: django_celery_beat/models.py:46
msgid "Astronomical dawn"
msgstr "천문 박명"

#: django_celery_beat/models.py:47
msgid "Civil dawn"
msgstr "시민 박명"

#: django_celery_beat/models.py:48
msgid "Nautical dawn"
msgstr "항해 박명"

#: django_celery_beat/models.py:49
msgid "Astronomical dusk"
msgstr "천문 황혼"

#: django_celery_beat/models.py:50
msgid "Civil dusk"
msgstr "시민 황혼"

#: django_celery_beat/models.py:51
msgid "Nautical dusk"
msgstr "항해 황혼"

#: django_celery_beat/models.py:52
msgid "Solar noon"
msgstr "정오"

#: django_celery_beat/models.py:53
msgid "Sunrise"
msgstr "일출"

#: django_celery_beat/models.py:54
msgid "Sunset"
msgstr "일몰"

#: django_celery_beat/models.py:88
msgid "Solar Event"
msgstr "Solar Event"

#: django_celery_beat/models.py:89
msgid "The type of solar event when the job should run"
msgstr "태스크가 작동 해야 하는 Solar Event"

#: django_celery_beat/models.py:93
msgid "Latitude"
msgstr "위도"

#: django_celery_beat/models.py:94
msgid "Run the task when the event happens at this latitude"
msgstr "입력된 위도에서 Solar Event 가 발생하면 태스크를 실행합니다."

#: django_celery_beat/models.py:99
msgid "Longitude"
msgstr "경도"

#: django_celery_beat/models.py:100
msgid "Run the task when the event happens at this longitude"
msgstr "입력된 경도에서 Solar Event 가 발생하면 태스크를 실행합니다."

#: django_celery_beat/models.py:107
msgid "solar event"
msgstr "solar event"

#: django_celery_beat/models.py:108
msgid "solar events"
msgstr "solar events"

#: django_celery_beat/models.py:158
msgid "Number of Periods"
msgstr "매"

#: django_celery_beat/models.py:159
msgid "Number of interval periods to wait before running the task again"
msgstr "태스크의 다음 동작까지 몇 번의 간격을 기다릴 것인지 입력합니다."

#: django_celery_beat/models.py:165
msgid "Interval Period"
msgstr "간격"

#: django_celery_beat/models.py:166
msgid "The type of period between task runs (Example: days)"
msgstr "실행 간격 (예: 일, 시간)"

#: django_celery_beat/models.py:172
msgid "interval"
msgstr "interval"

#: django_celery_beat/models.py:173
msgid "intervals"
msgstr "intervals"

#: django_celery_beat/models.py:200
msgid "every {}"
msgstr "매 {}"

#: django_celery_beat/models.py:205
msgid "every {} {}"
msgstr "매 {} {}"

#: django_celery_beat/models.py:216
msgid "Clock Time"
msgstr "Clock Time"

#: django_celery_beat/models.py:217
msgid "Run the task at clocked time"
msgstr "Clock Time 기준으로 태스크를 작동합니다."

#: django_celery_beat/models.py:223 django_celery_beat/models.py:224
msgid "clocked"
msgstr "clocked"

#: django_celery_beat/models.py:264
msgid "Minute(s)"
msgstr "분"

#: django_celery_beat/models.py:266
msgid "Cron Minutes to Run. Use \"*\" for \"all\". (Example: \"0,30\")"
msgstr "Cron 의 분. 모두인 경우 \"*\". (예: \"0,30\")"

#: django_celery_beat/models.py:271
msgid "Hour(s)"
msgstr "시"

#: django_celery_beat/models.py:273
msgid "Cron Hours to Run. Use \"*\" for \"all\". (Example: \"8,20\")"
msgstr "Cron 의 시간. 모두인 경우 \"*\". (예: \"8,20\")"

#: django_celery_beat/models.py:278
msgid "Day(s) Of The Week"
msgstr "요일"

#: django_celery_beat/models.py:280
#, fuzzy
#| msgid ""
#| "Cron Days Of The Week to Run. Use \"*\" for \"all\". (Example: \"0,5\")"
msgid ""
"Cron Days Of The Week to Run. Use \"*\" for \"all\", Sunday is 0 or 7, "
"Monday is 1. (Example: \"0,5\")"
msgstr "Cron 의 요일. 모두인 경우 \"*\". (예: \"0,5\")"

#: django_celery_beat/models.py:286
msgid "Day(s) Of The Month"
msgstr "일"

#: django_celery_beat/models.py:288
msgid ""
"Cron Days Of The Month to Run. Use \"*\" for \"all\". (Example: \"1,15\")"
msgstr "Cron 의 일. 모두인 경우 \"*\". (예: \"1,15\")"

#: django_celery_beat/models.py:294
msgid "Month(s) Of The Year"
msgstr "월"

#: django_celery_beat/models.py:296
#, fuzzy
#| msgid ""
#| "Cron Months Of The Year to Run. Use \"*\" for \"all\". (Example: \"0,6\")"
msgid ""
"Cron Months (1-12) Of The Year to Run. Use \"*\" for \"all\". (Example: "
"\"1,12\")"
msgstr "Cron 의 월. 모두인 경우 \"*\"."

#: django_celery_beat/models.py:304
msgid "Cron Timezone"
msgstr "타임존"

#: django_celery_beat/models.py:306
msgid "Timezone to Run the Cron Schedule on. Default is UTC."
msgstr "Cron 의 타임존. 기본값은 UTC"

#: django_celery_beat/models.py:312
msgid "crontab"
msgstr "crontab"

#: django_celery_beat/models.py:313
msgid "crontabs"
msgstr "crontabs"

#: django_celery_beat/models.py:404
msgid "Name"
msgstr "태스크 이름"

#: django_celery_beat/models.py:405
msgid "Short Description For This Task"
msgstr "태스크의 간단한 설명을 작성합니다."

#: django_celery_beat/models.py:410
msgid ""
"The Name of the Celery Task that Should be Run.  (Example: \"proj.tasks."
"import_contacts\")"
msgstr ""
"실행 되어야 하는 셀러리 태스크의 이름 (예: \"proj.tasks.import_contacts\")"

#: django_celery_beat/models.py:418
msgid "Interval Schedule"
msgstr "Interval Schedule"

#: django_celery_beat/models.py:419
msgid ""
"Interval Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr ""
"태스크 실행 Interval Schedule. 한 종류의 스케줄 타입을 지정하면 나머지는 빈 "
"값으로 두어야 합니다."

#: django_celery_beat/models.py:424
msgid "Crontab Schedule"
msgstr "Crontab Schedule"

#: django_celery_beat/models.py:425
msgid ""
"Crontab Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr ""
"태스크 실행 Crontab Schedule. 한 종류의 스케줄 타입을 지정하면 나머지는 빈 값"
"으로 두어야 합니다."

#: django_celery_beat/models.py:430
msgid "Solar Schedule"
msgstr "Solar Schedule"

#: django_celery_beat/models.py:431
msgid ""
"Solar Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr ""
"태스크 실행을 위한 Solar Schedule. 한 종류의 스케줄 타입을 지정하면 나머지는 "
"빈 값으로 두어야 합니다."

#: django_celery_beat/models.py:436
msgid "Clocked Schedule"
msgstr "Clocked Schedule"

#: django_celery_beat/models.py:437
msgid ""
"Clocked Schedule to run the task on.  Set only one schedule type, leave the "
"others null."
msgstr ""
"태스크 실행을 위한 Clocked Schedule. 한 종류의 스케줄 타입을 지정하면 나머지"
"는 빈 값으로 두어야 합니다."

#: django_celery_beat/models.py:443
msgid "Positional Arguments"
msgstr "Positional Arguments"

#: django_celery_beat/models.py:445
msgid "JSON encoded positional arguments (Example: [\"arg1\", \"arg2\"])"
msgstr "JSON 형태로 인코딩된 위치 인자 (예: [\"arg1\", \"arg2\"])"

#: django_celery_beat/models.py:450
msgid "Keyword Arguments"
msgstr "Keyword Arguments"

#: django_celery_beat/models.py:452
msgid "JSON encoded keyword arguments (Example: {\"argument\": \"value\"})"
msgstr "JSON 형태로 인코딩된 키워드 인자 (예: {\"argument\": \"value\"})"

#: django_celery_beat/models.py:458
msgid "Queue Override"
msgstr "Queue Override"

#: django_celery_beat/models.py:460
msgid "Queue defined in CELERY_TASK_QUEUES. Leave None for default queuing."
msgstr "CELERY_TASK_QUEES 에 정의된 큐, 빈 값으로 두면 기본 큐를 사용합니다."

#: django_celery_beat/models.py:469
msgid "Exchange"
msgstr "Exchange"

#: django_celery_beat/models.py:470
msgid "Override Exchange for low-level AMQP routing"
msgstr "low-level AMQP 를 원하는 경우 Exchange 를 오버라이드 합니다."

#: django_celery_beat/models.py:474
msgid "Routing Key"
msgstr "Routing Key"

#: django_celery_beat/models.py:475
msgid "Override Routing Key for low-level AMQP routing"
msgstr "low-level AMQP 를 원하는 경우 Routing Key 를 오버라이드 합니다."

#: django_celery_beat/models.py:479
msgid "AMQP Message Headers"
msgstr "AMQP 메시지 헤더"

#: django_celery_beat/models.py:480
msgid "JSON encoded message headers for the AMQP message."
msgstr "AMQP 메시지를 위해 JSON 형식으로 인코딩 된 메시지 헤더"

#: django_celery_beat/models.py:486
msgid "Priority"
msgstr "Priority"

#: django_celery_beat/models.py:488
msgid ""
"Priority Number between 0 and 255. Supported by: RabbitMQ, Redis (priority "
"reversed, 0 is highest)."
msgstr ""
"0과 255사이의 우선 순위 숫자. RabbitMQ, Redis 에서 지원합니다. (0이 높은 우선"
"순위를 가집니다.)"

#: django_celery_beat/models.py:493
msgid "Expires Datetime"
msgstr "만료 일시"

#: django_celery_beat/models.py:495
msgid ""
"Datetime after which the schedule will no longer trigger the task to run"
msgstr "만료 일시 이후에는 태스크가 작동하지 않습니다."

#: django_celery_beat/models.py:500
msgid "Expires timedelta with seconds"
msgstr "초 단위 만료 시간"

#: django_celery_beat/models.py:502
msgid ""
"Timedelta with seconds which the schedule will no longer trigger the task to "
"run"
msgstr "입력된 만료 초가 지난 뒤에는 태스크가 작동하지 않습니다."

#: django_celery_beat/models.py:508
msgid "One-off Task"
msgstr "One-off 태스크"

#: django_celery_beat/models.py:510
msgid "If True, the schedule will only run the task a single time"
msgstr "체크된 경우, 태스크는 한 번만 실행 됩니다."

#: django_celery_beat/models.py:514
msgid "Start Datetime"
msgstr "시작 일시"

#: django_celery_beat/models.py:516
msgid "Datetime when the schedule should begin triggering the task to run"
msgstr "태스크 스케줄의 작동 시작 일시"

#: django_celery_beat/models.py:521
msgid "Enabled"
msgstr "활성화 여부"

#: django_celery_beat/models.py:522
msgid "Set to False to disable the schedule"
msgstr "체크를 해제하면 비활성화가 됩니다."

#: django_celery_beat/models.py:527
msgid "Last Run Datetime"
msgstr "최종 작동 일시"

#: django_celery_beat/models.py:529
msgid ""
"Datetime that the schedule last triggered the task to run. Reset to None if "
"enabled is set to False."
msgstr ""
"태스크가 최종적으로 작동한 시간. 만약 테스크가 비활성화 된 경우 None 으로 지"
"정됩니다."

#: django_celery_beat/models.py:534
msgid "Total Run Count"
msgstr "실행 횟수"

#: django_celery_beat/models.py:536
msgid "Running count of how many times the schedule has triggered the task"
msgstr "태스크의 실행 횟수"

#: django_celery_beat/models.py:541
msgid "Last Modified"
msgstr "최종 변경 일시"

#: django_celery_beat/models.py:542
msgid "Datetime that this PeriodicTask was last modified"
msgstr "태스크가 최종적으로 변경된 일시"

#: django_celery_beat/models.py:546
msgid "Description"
msgstr "설명"

#: django_celery_beat/models.py:548
msgid "Detailed description about the details of this Periodic Task"
msgstr "태스크에 대한 상세 설명을 작성합니다."

#: django_celery_beat/models.py:557
msgid "periodic task"
msgstr "periodic task"

#: django_celery_beat/models.py:558
msgid "periodic tasks"
msgstr "periodic tasks"

#: django_celery_beat/templates/admin/djcelery/change_list.html:6
msgid "Home"
msgstr "Home"
