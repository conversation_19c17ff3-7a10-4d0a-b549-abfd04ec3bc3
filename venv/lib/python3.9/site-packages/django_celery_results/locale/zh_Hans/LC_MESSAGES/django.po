# Simplified Chinese translation strings for django-celery-results.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as django-celery-results.
# <<EMAIL>>, 2021.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version:\n"
"Report-Msgid-Bugs-To:\n"
"POT-Creation-Date: 2021-11-20 22:00+0800\n"
"PO-Revision-Date: 2021-11-20 23:00+0800\n"
"Last-Translator: ifmos <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: zh-hans\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: django_celery_results/admin.py:39
msgid "Parameters"
msgstr "参数"

#: django_celery_results/admin.py:46
msgid "Result"
msgstr "结果"

#: django_celery_results/apps.py:15
msgid "Celery Results"
msgstr "Celery 结果"

#: django_celery_results/models.py:28
msgid "Task ID"
msgstr "任务 ID"

#: django_celery_results/models.py:29
msgid "Celery ID for the Task that was run"
msgstr "已运行任务 Celery ID"

#: django_celery_results/models.py:32
msgid "Task Name"
msgstr "任务名称"

#: django_celery_results/models.py:33
msgid "Name of the Task which was run"
msgstr "已运行任务名称"

#: django_celery_results/models.py:36
msgid "Task Positional Arguments"
msgstr "任务位置参数"

#: django_celery_results/models.py:37
msgid "JSON representation of the positional arguments used with the task"
msgstr "该任务位置参数的 JSON 字符串"

#: django_celery_results/models.py:41
msgid "Task Named Arguments"
msgstr "任务具名参数"

#: django_celery_results/models.py:42
msgid "JSON representation of the named arguments used with the task"
msgstr "该任务具名参数的 JSON 字符串"

#: django_celery_results/models.py:47
msgid "Task State"
msgstr "任务状态"

#: django_celery_results/models.py:48
msgid "Current state of the task being run"
msgstr "运行中任务的当前状态"

#: django_celery_results/models.py:51
msgid "Worker"
msgstr "Worker"

#: django_celery_results/models.py:51
msgid "Worker that executes the task"
msgstr "执行该任务的 Worker"

#: django_celery_results/models.py:55
msgid "Result Content Type"
msgstr "结果内容类型"

#: django_celery_results/models.py:56
msgid "Content type of the result data"
msgstr "结果数据的内容类型"

#: django_celery_results/models.py:59
msgid "Result Encoding"
msgstr "结果编码格式"

#: django_celery_results/models.py:60
msgid "The encoding used to save the task result data"
msgstr "保存结果数据的编码格式"

#: django_celery_results/models.py:63
msgid "Result Data"
msgstr "结果数据"

#: django_celery_results/models.py:64
msgid ""
"The data returned by the task.  Use content_encoding and content_type fields"
" to read."
msgstr "该任务返回数据，根据 content_encoding 和 content_type 字段读取。"

#: django_celery_results/models.py:68
msgid "Created DateTime"
msgstr "创建时间"

#: django_celery_results/models.py:69
msgid "Datetime field when the task result was created in UTC"
msgstr "UTC格式的任务创建时间字段"

#: django_celery_results/models.py:72
msgid "Completed DateTime"
msgstr "完成时间"

#: django_celery_results/models.py:73
msgid "Datetime field when the task was completed in UTC"
msgstr "UTC格式的任务完成时间字段"

#: django_celery_results/models.py:76
msgid "Traceback"
msgstr "Traceback"

#: django_celery_results/models.py:77
msgid "Text of the traceback if the task generated one"
msgstr "任务生成报错时的 traceback 文本"

#: django_celery_results/models.py:80
msgid "Task Meta Information"
msgstr "任务元信息"

#: django_celery_results/models.py:81
msgid ""
"JSON meta information about the task, such as information on child tasks"
msgstr "关于该任务的 JSON 元信息，如子任务的信息"

#: django_celery_results/models.py:91
msgid "task result"
msgstr "任务结果"

#: django_celery_results/models.py:92
msgid "task results"
msgstr "任务结果"

#: django_celery_results/models.py:129 django_celery_results/models.py:171
msgid "Group ID"
msgstr "分组 ID"

#: django_celery_results/models.py:130
msgid "Celery ID for the Chord header group"
msgstr "Chord header 分组的 Celery ID"

#: django_celery_results/models.py:134
msgid ""
"JSON serialized list of task result tuples. use .group_result() to decode"
msgstr ""
"任务结果元组的 JSON 序列化列表。使用 .group_result() 进行解码"

#: django_celery_results/models.py:140
msgid "Starts at len(chord header) and decrements after each task is finished"
msgstr "在 len(chord header) 处开始并且会在每个任务结束后递减"

#: django_celery_results/models.py:172
msgid "Celery ID for the Group that was run"
msgstr "已运行分组的 Celery ID"

#: django_celery_results/models.py:177
msgid "Datetime field when the group result was created in UTC"
msgstr "分组结果创建时的 UTC 格式 datetime 字段"

#: django_celery_results/models.py:182
msgid "Datetime field when the group was completed in UTC"
msgstr "分组结果完成时的 UTC 格式 datetime 字段"

#: django_celery_results/models.py:217
msgid "group result"
msgstr "分组结果"

#: django_celery_results/models.py:218
msgid "group results"
msgstr "分组结果"
