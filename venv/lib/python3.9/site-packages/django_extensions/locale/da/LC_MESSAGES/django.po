# django_extentions in Danish.
# django_extensions på <PERSON>.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2009.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2011-02-02 11:42+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: admin/__init__.py:121
msgid "and"
msgstr "og"

#: admin/__init__.py:123
#, python-format
msgid ""
"Use the left field to do %(model_name)s lookups in the fields %(field_list)s."
msgstr ""
"Brug feltet til venstre til at lave %(model_name)s lookups i felterne %"
"(field_list)s."

#: db/models.py:15
msgid "created"
msgstr "skabt"

#: db/models.py:16
msgid "modified"
msgstr "ændret"

#: db/models.py:26
msgid "title"
msgstr "titel"

#: db/models.py:27
msgid "slug"
msgstr "slug"

#: db/models.py:28
msgid "description"
msgstr "beskrivelse"

#: db/models.py:50
msgid "Inactive"
msgstr ""

#: db/models.py:51
msgid "Active"
msgstr ""

#: db/models.py:53
msgid "status"
msgstr ""

#: db/models.py:56
msgid "keep empty for an immediate activation"
msgstr ""

#: db/models.py:58
msgid "keep empty for indefinite activation"
msgstr ""

#: management/commands/show_urls.py:34
#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr ""

#: templates/django_extensions/widgets/foreignkey_searchinput.html:4
msgid "Lookup"
msgstr "Lookup"
